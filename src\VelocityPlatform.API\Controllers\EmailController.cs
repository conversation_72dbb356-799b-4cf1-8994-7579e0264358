using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs;

namespace VelocityPlatform.API.Controllers;

[ApiController]
[Route("api/v1/[controller]")]
public class EmailController : ControllerBase
{
    private readonly IEmailService _emailService;
    private readonly ILogger<EmailController> _logger;

    public EmailController(
        IEmailService emailService,
        ILogger<EmailController> logger)
    {
        _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// Sends a custom email with flexible content options.
    /// Requires authentication.
    /// </summary>
    [HttpPost("send")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<object>>> SendEmail([FromBody] SendEmailRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
            return BadRequest(ApiResponse<object>.FailureResponse(string.Join(" ", errors)));
        }

        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            
            _logger.LogInformation("Email send request to {Email} with subject '{Subject}' by user {UserId} ({UserEmail})", 
                request.ToEmail, request.Subject, userId, userEmail);
            
            await _emailService.SendCustomEmailAsync(
                request.ToEmail, 
                request.Subject, 
                request.Message, 
                request.IsHtml ?? false,
                request.FromName);
            
            _logger.LogInformation("Email sent successfully to {Email} by user {UserId}", request.ToEmail, userId);
            return Ok(ApiResponse<object>.SuccessResponse(null, $"Email sent successfully to {request.ToEmail}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send email to {Email}", request.ToEmail);
            return StatusCode(500, ApiResponse<object>.FailureResponse($"Failed to send email: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a test email to verify SMTP configuration.
    /// For development and testing purposes.
    /// </summary>
    [HttpPost("test")]
    [AllowAnonymous] // For testing purposes
    public async Task<ActionResult<ApiResponse<object>>> SendTestEmail([FromBody] TestEmailRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
            return BadRequest(ApiResponse<object>.FailureResponse(string.Join(" ", errors)));
        }

        try
        {
            _logger.LogInformation("Test email request to {Email} with subject '{Subject}'", request.ToEmail, request.Subject);
            
            await _emailService.SendTestEmailAsync(request.ToEmail, request.Subject, request.Message);
            
            _logger.LogInformation("Test email sent successfully to {Email}", request.ToEmail);
            return Ok(ApiResponse<object>.SuccessResponse(null, $"Test email sent successfully to {request.ToEmail}"));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to send test email to {Email}", request.ToEmail);
            return StatusCode(500, ApiResponse<object>.FailureResponse($"Failed to send test email: {ex.Message}"));
        }
    }

    /// <summary>
    /// Sends a bulk email to multiple recipients.
    /// Requires authentication and appropriate permissions.
    /// </summary>
    [HttpPost("bulk")]
    [Authorize]
    public async Task<ActionResult<ApiResponse<object>>> SendBulkEmail([FromBody] BulkEmailRequestDto request)
    {
        if (!ModelState.IsValid)
        {
            var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
            return BadRequest(ApiResponse<object>.FailureResponse(string.Join(" ", errors)));
        }

        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
            
            _logger.LogInformation("Bulk email request to {Count} recipients with subject '{Subject}' by user {UserId} ({UserEmail})", 
                request.ToEmails.Count, request.Subject, userId, userEmail);

            var successCount = 0;
            var failureCount = 0;
            var errors = new List<string>();

            foreach (var email in request.ToEmails)
            {
                try
                {
                    await _emailService.SendCustomEmailAsync(
                        email, 
                        request.Subject, 
                        request.Message, 
                        request.IsHtml ?? false,
                        request.FromName);
                    successCount++;
                }
                catch (Exception ex)
                {
                    failureCount++;
                    errors.Add($"{email}: {ex.Message}");
                    _logger.LogError(ex, "Failed to send bulk email to {Email}", email);
                }
            }
            
            _logger.LogInformation("Bulk email completed: {Success} successful, {Failures} failed", successCount, failureCount);
            
            var result = new
            {
                TotalEmails = request.ToEmails.Count,
                SuccessCount = successCount,
                FailureCount = failureCount,
                Errors = errors
            };

            if (failureCount == 0)
            {
                return Ok(ApiResponse<object>.SuccessResponse(result, $"All {successCount} emails sent successfully"));
            }
            else if (successCount > 0)
            {
                return Ok(ApiResponse<object>.SuccessResponse(result, $"{successCount} emails sent successfully, {failureCount} failed"));
            }
            else
            {
                return StatusCode(500, ApiResponse<object>.FailureResponse("All emails failed to send"));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process bulk email request");
            return StatusCode(500, ApiResponse<object>.FailureResponse($"Failed to process bulk email request: {ex.Message}"));
        }
    }
}

public class BulkEmailRequestDto
{
    [Required(ErrorMessage = "At least one email address is required.")]
    [MinLength(1, ErrorMessage = "At least one email address is required.")]
    [MaxLength(100, ErrorMessage = "Cannot send to more than 100 recipients at once.")]
    public List<string> ToEmails { get; set; } = new List<string>();

    [Required(ErrorMessage = "Subject is required.")]
    [StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters.")]
    public string Subject { get; set; } = string.Empty;

    [Required(ErrorMessage = "Message is required.")]
    [StringLength(10000, ErrorMessage = "Message cannot exceed 10000 characters.")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Whether the message content is HTML. Default is false (plain text).
    /// </summary>
    public bool? IsHtml { get; set; } = false;

    /// <summary>
    /// Optional custom sender name. If not provided, uses default platform name.
    /// </summary>
    [StringLength(100, ErrorMessage = "From name cannot exceed 100 characters.")]
    public string? FromName { get; set; }
}
