using System;
using Npgsql;

namespace CreateDefaultTenant;

class Program
{
    static void Main()
    {
        var connectionString = "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1";
        
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            connection.Open();
            
            Console.WriteLine("Connected to database successfully!");

            // First, create Tenants table if it doesn't exist
            var createTableSql = @"
                CREATE TABLE IF NOT EXISTS ""Tenants"" (
                    ""Id"" uuid PRIMARY KEY,
                    ""Name"" character varying(255) NOT NULL,
                    ""Domain"" character varying(100) NOT NULL,
                    ""Slug"" character varying(100),
                    ""Status"" integer NOT NULL DEFAULT 0,
                    ""CreatedAt"" timestamp with time zone NOT NULL,
                    ""UpdatedAt"" timestamp with time zone NOT NULL,
                    ""IsActive"" boolean NOT NULL DEFAULT true,
                    ""MaxSites"" integer NOT NULL DEFAULT 0,
                    ""MaxUsers"" integer NOT NULL DEFAULT 0,
                    ""SubscriptionPlan"" character varying(50),
                    ""IsolationLevel"" character varying(50),
                    ""IsolationEnforcedDate"" timestamp with time zone
                )";

            using var createTableCommand = new NpgsqlCommand(createTableSql, connection);
            createTableCommand.ExecuteNonQuery();
            Console.WriteLine("Tenants table created or already exists!");

            // Check what columns exist
            var columnsSql = @"
                SELECT column_name
                FROM information_schema.columns
                WHERE table_name = 'Tenants' AND table_schema = 'public'
                ORDER BY ordinal_position";
            using var columnsCommand = new NpgsqlCommand(columnsSql, connection);
            using var reader = columnsCommand.ExecuteReader();
            Console.WriteLine("Columns in Tenants table:");
            while (reader.Read())
            {
                Console.WriteLine($"  - {reader.GetString(0)}");
            }
            reader.Close();

            // Check if default tenant already exists
            var checkSql = "SELECT COUNT(*) FROM \"Tenants\" WHERE \"Slug\" = 'default'";
            using var checkCommand = new NpgsqlCommand(checkSql, connection);
            var count = (long)checkCommand.ExecuteScalar();
            
            if (count > 0)
            {
                Console.WriteLine("Default tenant already exists!");
                return;
            }
            
            // Create default tenant
            var tenantId = Guid.NewGuid();
            var insertSql = @"
                INSERT INTO ""Tenants"" (""Id"", ""Name"", ""Slug"", ""SubscriptionPlan"", ""IsolationLevel"", ""DataIsolationPolicy"", ""IsolationPolicy"", ""TenantId"", ""CreatedAt"", ""UpdatedAt"", ""IsActive"", ""MaxSites"", ""MaxUsers"")
                VALUES (@id, @name, @slug, @subscriptionPlan, @isolationLevel, @dataIsolationPolicy, @isolationPolicy, @tenantId, @createdAt, @updatedAt, @isActive, @maxSites, @maxUsers)";

            using var command = new NpgsqlCommand(insertSql, connection);
            command.Parameters.AddWithValue("@id", tenantId);
            command.Parameters.AddWithValue("@name", "Default Tenant");
            command.Parameters.AddWithValue("@slug", "default");
            command.Parameters.AddWithValue("@subscriptionPlan", "free");
            command.Parameters.AddWithValue("@isolationLevel", "shared");
            command.Parameters.AddWithValue("@dataIsolationPolicy", "shared");
            command.Parameters.AddWithValue("@isolationPolicy", "shared");
            command.Parameters.AddWithValue("@tenantId", tenantId); // Self-reference
            command.Parameters.AddWithValue("@createdAt", DateTime.UtcNow);
            command.Parameters.AddWithValue("@updatedAt", DateTime.UtcNow);
            command.Parameters.AddWithValue("@isActive", true);
            command.Parameters.AddWithValue("@maxSites", 10);
            command.Parameters.AddWithValue("@maxUsers", 100);
            
            command.ExecuteNonQuery();
            
            Console.WriteLine($"Successfully created default tenant with ID: {tenantId}");
            
            // Update existing test user to use default tenant
            var updateUserSql = @"UPDATE ""AspNetUsers"" SET ""TenantId"" = @tenantId WHERE ""Email"" = '<EMAIL>'";
            using var updateCommand = new NpgsqlCommand(updateUserSql, connection);
            updateCommand.Parameters.AddWithValue("@tenantId", tenantId);
            var rowsUpdated = updateCommand.ExecuteNonQuery();
            
            if (rowsUpdated > 0)
            {
                Console.WriteLine("Updated test user to use default tenant!");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
