using AutoMapper;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
// using VelocityPlatform.Business.Services.Contracts; // Removed this line
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities; // Assuming User entity is here
using VelocityPlatform.Models.Enums; // Assuming UserRoleEnum etc. are here
using VelocityPlatform.Business.Exceptions; // Added for custom exceptions

namespace VelocityPlatform.Business.Services
{
    public class UserService : IUserService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly UserManager<User> _userManager;
        private readonly RoleManager<IdentityRole<Guid>> _roleManager; // Using IdentityRole<Guid> to match Program.cs configuration
        private readonly IMapper _mapper;
        private readonly ILogger<UserService> _logger;
        // private readonly IFileStorageService _fileStorageService; // If avatar logic becomes complex

        public UserService(
            VelocityPlatformDbContext context,
            UserManager<User> userManager,
            RoleManager<IdentityRole<Guid>> roleManager,
            IMapper mapper,
            ILogger<UserService> logger)
        {
            _context = context;
            _userManager = userManager;
            _roleManager = roleManager;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<PagedResponseDto<UserProfileDto>> GetUsersAsync(Guid? tenantId, int pageNumber, int pageSize, Guid currentUserId, bool isPlatformAdmin, string? sortBy = null, string? sortOrder = "asc", string? filter = null, string? searchTerm = null)
        {
            _logger.LogInformation("Fetching users. TenantId: {TenantId}, Page: {PageNumber}, PageSize: {PageSize}, CurrentUserId: {CurrentUserId}, IsPlatformAdmin: {IsPlatformAdmin}", tenantId, pageNumber, pageSize, currentUserId, isPlatformAdmin);
            
            IQueryable<User> query = _userManager.Users;

            if (!isPlatformAdmin)
            {
                // Non-admins might only see users in their own tenant or based on other specific logic
                // For now, let's assume they can only see users if tenantId is provided and matches their tenant
                // This logic needs to be refined based on actual multi-tenancy rules
                var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString());
                if (currentUser == null || currentUser.TenantId != tenantId)
                {
                     _logger.LogWarning("User {CurrentUserId} attempted to access users for tenant {TenantId} without sufficient permissions.", currentUserId, tenantId);
                    return new PagedResponseDto<UserProfileDto>(Enumerable.Empty<UserProfileDto>(), 0, pageNumber, pageSize); // Or throw UnauthorizedAccessException
                }
                query = query.Where(u => u.TenantId == tenantId);
            }
            else if (tenantId.HasValue)
            {
                query = query.Where(u => u.TenantId == tenantId.Value);
            }
            // Apply filtering
            if (!string.IsNullOrWhiteSpace(filter) && !string.IsNullOrWhiteSpace(searchTerm))
            {
                query = filter.ToLowerInvariant() switch
                {
                    "email" => query.Where(u => u.Email != null && u.Email.Contains(searchTerm)),
                    "firstname" => query.Where(u => u.FirstName != null && u.FirstName.Contains(searchTerm)),
                    "lastname" => query.Where(u => u.LastName != null && u.LastName.Contains(searchTerm)),
                    "username" => query.Where(u => u.UserName != null && u.UserName.Contains(searchTerm)),
                    _ => query // No valid filter, return original query
                };
            }
 
            // Apply sorting
            if (!string.IsNullOrWhiteSpace(sortBy))
            {
                query = sortBy.ToLowerInvariant() switch
                {
                    "email" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.Email) : query.OrderBy(u => u.Email),
                    "firstname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.FirstName) : query.OrderBy(u => u.FirstName),
                    "lastname" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.LastName) : query.OrderBy(u => u.LastName),
                    "createdat" => sortOrder?.ToLowerInvariant() == "desc" ? query.OrderByDescending(u => u.CreatedAt) : query.OrderBy(u => u.CreatedAt),
                    _ => query.OrderByDescending(u => u.CreatedAt) // Default sort if sortBy is invalid
                };
            }
            else
            {
                query = query.OrderByDescending(u => u.CreatedAt); // Default sort if no sortBy is provided
            }
 
            var totalCount = await query.CountAsync();
            var users = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
            
            var userDtos = _mapper.Map<IEnumerable<UserProfileDto>>(users);
 
            return new PagedResponseDto<UserProfileDto>(userDtos, totalCount, pageNumber, pageSize);
        }

        public async Task<UserProfileDto?> GetUserByIdAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Fetching user by ID: {UserId}. CurrentUserId: {CurrentUserId}, IsPlatformAdmin: {IsPlatformAdmin}", userId, currentUserId, isPlatformAdmin);
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                throw new ResourceNotFoundException($"User with ID {userId} not found.");
            }

            if (!isPlatformAdmin)
            {
                var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString());
                // Basic check: users can see their own profile, or admins can see any.
                // Tenant-specific logic might be needed here too.
                if (userId != currentUserId && (currentUser == null || user.TenantId != currentUser.TenantId))
                {
                    _logger.LogWarning("User {CurrentUserId} attempted to access user {UserId} without sufficient permissions.", currentUserId, userId);
                    throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to access user with ID {userId}.");
                }
            }
            return _mapper.Map<UserProfileDto>(user);
        }

        public async Task<UserProfileDto?> GetUserByEmailAsync(string email)
        {
            _logger.LogInformation("Fetching user by email: {Email}", email);
            var user = await _userManager.FindByEmailAsync(email);
            return _mapper.Map<UserProfileDto>(user);
        }

        public async Task<UserProfileDto?> GetCurrentUserProfileAsync(Guid currentUserId)
        {
            _logger.LogInformation("Fetching current user profile for ID: {CurrentUserId}", currentUserId);
            var user = await _userManager.FindByIdAsync(currentUserId.ToString());
            return _mapper.Map<UserProfileDto>(user);
        }

        public async Task<UserProfileDto> CreateUserAsync(UserCreateDto userCreateDto, Guid currentUserId, Guid? tenantId)
        {
            _logger.LogInformation("Creating user. Email: {Email}, TenantId: {TenantId}, RequestedBy: {CurrentUserId}", userCreateDto.Email, tenantId, currentUserId);
            
            var user = _mapper.Map<User>(userCreateDto);
            user.Id = Guid.NewGuid(); // ASP.NET Core Identity uses string IDs
            user.UserName = userCreateDto.Email; // Often username is same as email
            user.TenantId = tenantId ?? Guid.Empty; // Assign tenant if provided

            var result = await _userManager.CreateAsync(user, userCreateDto.Password);
            if (!result.Succeeded)
            {
                _logger.LogError("User creation failed: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
                throw new BusinessValidationException($"User creation failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }

            // Add roles if specified
            if (userCreateDto.Roles != null && userCreateDto.Roles.Any())
            {
                foreach (var roleName in userCreateDto.Roles)
                {
                    if (await _roleManager.RoleExistsAsync(roleName))
                    {
                        await _userManager.AddToRoleAsync(user, roleName);
                    }
                    else
                    {
                        _logger.LogWarning("Role {RoleName} not found during user creation.", roleName);
                    }
                }
            }
            
            _logger.LogInformation("User {UserId} created successfully by {CurrentUserId}", user.Id, currentUserId);
            return _mapper.Map<UserProfileDto>(user);
        }

        public async Task<bool> UpdateUserAsync(Guid userIdToUpdate, UserUpdateDto updateUserDto, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Updating user {UserIdToUpdate} by {CurrentUserId}. IsPlatformAdmin: {IsPlatformAdmin}", userIdToUpdate, currentUserId, isPlatformAdmin);
            var user = await _userManager.FindByIdAsync(userIdToUpdate.ToString());
            if (user == null)
            {
                _logger.LogWarning("User {UserIdToUpdate} not found for update.", userIdToUpdate);
                throw new ResourceNotFoundException($"User with ID {userIdToUpdate} not found.");
            }

            if (!isPlatformAdmin && userIdToUpdate != currentUserId)
            {
                // Non-admins can only update their own profile.
                // Additional tenant checks might be needed.
                var currentUserRequesting = await _userManager.FindByIdAsync(currentUserId.ToString());
                 if (currentUserRequesting == null || user.TenantId != currentUserRequesting.TenantId)
                 {
                    _logger.LogWarning("User {CurrentUserId} attempted to update user {UserIdToUpdate} without sufficient permissions.", currentUserId, userIdToUpdate);
                    throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to update user with ID {userIdToUpdate}.");
                 }
            }

            _mapper.Map(updateUserDto, user); // Apply updates from DTO to entity

            // Handle potential username/email changes if allowed
            if (!string.IsNullOrWhiteSpace(updateUserDto.Email) && user.Email != updateUserDto.Email)
            {
                user.Email = updateUserDto.Email;
                user.UserName = updateUserDto.Email; // Assuming username is email
            }
            // Update other properties as needed from updateUserDto

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                _logger.LogError("User update failed for {UserIdToUpdate}: {Errors}", userIdToUpdate, string.Join(", ", result.Errors.Select(e => e.Description)));
                throw new BusinessValidationException($"User update failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }
            return result.Succeeded;
        }

        public async Task<bool> DeactivateUserAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Deactivating user {UserId} by {CurrentUserId}. IsPlatformAdmin: {IsPlatformAdmin}", userId, currentUserId, isPlatformAdmin);
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found for deactivation.", userId);
                throw new ResourceNotFoundException($"User with ID {userId} not found.");
            }

            if (!isPlatformAdmin)
            {
                // Typically only admins can deactivate users, or users themselves under certain conditions.
                // For now, let's assume only platform admins can.
                _logger.LogWarning("User {CurrentUserId} attempted to deactivate user {UserId} without admin rights.", currentUserId, userId);
                throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to deactivate user with ID {userId}.");
            }

            user.IsActive = false; // Assuming an 'IsActive' property on the User entity
            user.LockoutEnd = DateTimeOffset.MaxValue; // Another way to "deactivate"
            var result = await _userManager.UpdateAsync(user);

            if (!result.Succeeded)
            {
                _logger.LogError("Failed to deactivate user {UserId}: {Errors}", userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                throw new BusinessValidationException($"Failed to deactivate user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }
            return result.Succeeded;
        }

        public async Task<bool> ChangePasswordAsync(Guid userId, ChangePasswordDto changePasswordDto, Guid currentUserId)
        {
            _logger.LogInformation("Changing password for user {UserId} by {CurrentUserId}", userId, currentUserId);
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found for password change.", userId);
                throw new ResourceNotFoundException($"User with ID {userId} not found.");
            }

            // User can change their own password, or an admin can.
            if (userId != currentUserId)
            {
                var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString()); // Corrected typo: FindByIdByIdAsync to FindByIdAsync
                var isCurrentUserAdmin = currentUser != null && await _userManager.IsInRoleAsync(currentUser, "Admin"); // Assuming "Admin" role
                if (!isCurrentUserAdmin)
                {
                    _logger.LogWarning("User {CurrentUserId} attempted to change password for user {UserId} without sufficient permissions.", currentUserId, userId);
                    throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to change password for user with ID {userId}.");
                }
            }
            
            var token = await _userManager.GeneratePasswordResetTokenAsync(user);
            var result = await _userManager.ResetPasswordAsync(user, token, changePasswordDto.NewPassword);

            if (!result.Succeeded)
            {
                _logger.LogError("Password change failed for user {UserId}: {Errors}", userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                throw new BusinessValidationException($"Password change failed: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }
            return result.Succeeded;
        }

        public async Task<DataExportDto?> ExportUserDataAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Exporting data for user {UserId} by {CurrentUserId}. IsPlatformAdmin: {IsPlatformAdmin}", userId, currentUserId, isPlatformAdmin);
            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found for data export.", userId);
                throw new ResourceNotFoundException($"User with ID {userId} not found.");
            }

            if (!isPlatformAdmin && userId != currentUserId)
            {
                _logger.LogWarning("User {CurrentUserId} attempted to export data for user {UserId} without sufficient permissions.", currentUserId, userId);
                throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to export data for user with ID {userId}.");
            }

            // Gather all user-related data. This is a simplified example.
            // In a real scenario, you'd query various tables (profile, orders, activity, etc.)
            var userData = new
            {
                Profile = _mapper.Map<UserProfileDto>(user),
                // Consents = await GetUserConsentsAsync(userId, currentUserId, isPlatformAdmin), // Example of related data
                // Preferences = await GetUserPreferencesAsync(userId, currentUserId) // Example
            };

            var jsonData = JsonSerializer.Serialize(userData, new JsonSerializerOptions { WriteIndented = true });
            var fileContent = System.Text.Encoding.UTF8.GetBytes(jsonData);

            return new DataExportDto
            {
                FileName = $"user_data_{userId}_{DateTime.UtcNow:yyyyMMddHHmmss}.json",
                ContentType = "application/json",
                Content = fileContent
            };
        }

        public async Task<bool> AnonymizeUserDataAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin)
        {
            _logger.LogInformation("Anonymizing user data for {UserId} by {CurrentUserId}. IsPlatformAdmin: {IsPlatformAdmin}", userId, currentUserId, isPlatformAdmin);
            
            if (!isPlatformAdmin)
            {
                _logger.LogWarning("User {CurrentUserId} attempted to anonymize user {UserId} without admin rights.", currentUserId, userId);
                throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to anonymize user with ID {userId}.");
            }

            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                _logger.LogWarning("User {UserId} not found for anonymization.", userId);
                throw new ResourceNotFoundException($"User with ID {userId} not found.");
            }

            // Anonymization logic:
            user.UserName = $"anonymized_{Guid.NewGuid()}";
            user.NormalizedUserName = user.UserName.ToUpperInvariant();
            user.Email = $"anonymized_{Guid.NewGuid()}@example.com";
            user.NormalizedEmail = user.Email.ToUpperInvariant();
            user.PasswordHash = null; // Or a dummy hash
            user.SecurityStamp = Guid.NewGuid().ToString();
            user.FirstName = "Anonymous"; // Assuming FirstName property
            user.LastName = "User";     // Assuming LastName property
            user.PhoneNumber = null;
            // Clear or anonymize other PII fields in the User entity and related tables.
            // This is highly dependent on the data model.

            // Mark as inactive or anonymized
            user.IsActive = false; 
            user.LockoutEnd = DateTimeOffset.MaxValue;

            var result = await _userManager.UpdateAsync(user);
            if (!result.Succeeded)
            {
                _logger.LogError("Failed to anonymize user {UserId}: {Errors}", userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                throw new BusinessValidationException($"Failed to anonymize user: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }
            return result.Succeeded;
        }

        public async Task<PagedResponseDto<ConsentDto>> GetUserConsentsAsync(Guid userId, Guid currentUserId, bool isPlatformAdmin, int pageNumber = 1, int pageSize = 10)
        {
            _logger.LogInformation("Fetching consents for user {UserId} by {CurrentUserId}. IsPlatformAdmin: {IsPlatformAdmin}", userId, currentUserId, isPlatformAdmin);
            
            if (!isPlatformAdmin && userId != currentUserId)
            {
                _logger.LogWarning("User {CurrentUserId} attempted to get consents for user {UserId} without sufficient permissions.", currentUserId, userId);
                throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to access consents for user with ID {userId}.");
            }

            // Assuming Consent entity and relationship with User
            var query = _context.Consents
                                         .Where(c => c.UserId == userId.ToString()) // Assuming UserId is string in Consent entity
                                         .AsQueryable();

            var totalCount = await query.CountAsync();
            var consents = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var consentDtos = _mapper.Map<IEnumerable<ConsentDto>>(consents);

            return new PagedResponseDto<ConsentDto>(consentDtos, totalCount, pageNumber, pageSize);
        }

        public async Task<bool> UpdateConsentAsync(Guid userId, ConsentUpdateDto consentUpdateDto, string? ipAddress, string? userAgent, Guid currentUserId)
        {
            _logger.LogInformation("Updating consent for user {UserId}. ConsentType: {ConsentType}, Granted: {Granted}. By {CurrentUserId}, IP: {IP}, UserAgent: {UserAgent}", 
                userId, consentUpdateDto.ConsentType, consentUpdateDto.Granted, currentUserId, ipAddress, userAgent);

            if (userId != currentUserId)
            {
                // Check if current user is an admin if they are updating consent for another user
                var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString());
                var isCurrentUserAdmin = currentUser != null && await _userManager.IsInRoleAsync(currentUser, "Admin");
                if (!isCurrentUserAdmin)
                {
                    _logger.LogWarning("User {CurrentUserId} attempted to update consent for user {UserId} without sufficient permissions.", currentUserId, userId);
                    throw new ForbiddenAccessException($"User with ID {currentUserId} does not have permission to update consent for user with ID {userId}.");
                }
            }
            
            // Find existing consent or create new
            var consent = await _context.Consents
                                        .FirstOrDefaultAsync(c => c.UserId == userId.ToString() && c.ConsentType == (ConsentType)Enum.Parse(typeof(ConsentType), consentUpdateDto.ConsentType));

            if (consent == null)
            {
                consent = new Consent // Assuming Consent entity structure
                {
                    Id = Guid.NewGuid(),
                    UserId = userId.ToString(),
                    ConsentType = (ConsentType)Enum.Parse(typeof(ConsentType), consentUpdateDto.ConsentType),
                };
                _context.Consents.Add(consent);
            }

            consent.Granted = consentUpdateDto.Granted;
            consent.DateRecorded = DateTime.UtcNow;
            consent.IpAddress = ipAddress;
            consent.UserAgent = userAgent;
            // consent.Source = "UserInteraction"; // Or some other source identifier

            try
            {
                await _context.SaveChangesAsync();
                _logger.LogInformation("Consent for user {UserId}, type {ConsentType} updated to {Granted}.", userId, consentUpdateDto.ConsentType, consentUpdateDto.Granted);
                return true;
            }
            catch (DbUpdateException ex)
            {
                _logger.LogError(ex, "Error updating consent for user {UserId}, type {ConsentType}.", userId, consentUpdateDto.ConsentType);
                throw new BusinessValidationException($"Error updating consent: {ex.Message}");
            }
        }

        public async Task<Dictionary<string, object>> GetUserPreferencesAsync(Guid userId, Guid currentUserId)
        {
            _logger.LogInformation("Fetching preferences for user {UserId} by {CurrentUserId}.", userId, currentUserId);
            if (userId != currentUserId)
            {
                // Add admin check or other authorization logic if needed
                var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString());
                var isCurrentUserAdmin = currentUser != null && await _userManager.IsInRoleAsync(currentUser, "Admin");
                if (!isCurrentUserAdmin) {
                    _logger.LogWarning("User {CurrentUserId} attempted to get preferences for user {UserId} without sufficient permissions.", currentUserId, userId);
                    throw new ForbiddenAccessException("Cannot access preferences of another user.");
                }
            }

            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null || string.IsNullOrEmpty(user.PreferencesJson))
            {
                throw new ResourceNotFoundException($"User with ID {userId} not found or has no preferences.");
            }

            try
            {
                return JsonSerializer.Deserialize<Dictionary<string, object>>(user.PreferencesJson) ?? new Dictionary<string, object>();
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error deserializing preferences for user {UserId}.", userId);
                throw new BusinessValidationException($"Error deserializing user preferences: {ex.Message}");
            }
        }

        public async Task UpdateUserPreferencesAsync(Guid userId, Dictionary<string, object> preferences, Guid currentUserId)
        {
            _logger.LogInformation("Updating preferences for user {UserId} by {CurrentUserId}.", userId, currentUserId);
            if (userId != currentUserId)
            {
                // Add admin check or other authorization logic if needed
                var currentUser = await _userManager.FindByIdAsync(currentUserId.ToString());
                var isCurrentUserAdmin = currentUser != null && await _userManager.IsInRoleAsync(currentUser, "Admin");
                if (!isCurrentUserAdmin) {
                    _logger.LogWarning("User {CurrentUserId} attempted to update preferences for user {UserId} without sufficient permissions.", currentUserId, userId);
                    throw new ForbiddenAccessException("Cannot update preferences of another user.");
                }
            }

            var user = await _userManager.FindByIdAsync(userId.ToString());
            if (user == null)
            {
                throw new ResourceNotFoundException($"User with ID {userId} not found.");
            }

            user.PreferencesJson = JsonSerializer.Serialize(preferences);
            var result = await _userManager.UpdateAsync(user);

            if (!result.Succeeded)
            {
                _logger.LogError("Failed to update preferences for user {UserId}: {Errors}", userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                throw new BusinessValidationException($"Failed to update user preferences: {string.Join(", ", result.Errors.Select(e => e.Description))}");
            }
        }
    }
}