# API Endpoint Testing Checklist

## Authentication Endpoints ✅
- [x] POST /api/v1/Auth/login - **WORKING** (200 OK)
- [x] POST /api/v1/Auth/register - **WORKING** (200 OK)
- [x] POST /api/v1/Auth/refresh - **WORKING** (200 OK)

## AddonDefinitions Endpoints ✅
- [x] GET /api/v1/AddonDefinitions - **FIXED** (200 OK) - Was 500, fixed Entity Framework issues
- [x] GET /api/v1/AddonDefinitions/{id} - **WORKING** (404 for non-existent)
- [x] GET /api/v1/AddonDefinitions/pending - **WORKING** (403 for non-admin)

## Next Endpoints to Test

### Users Endpoints 🔄
- [x] GET /api/v1/Users/<USER>
- [x] GET /api/v1/Users/<USER>/preferences - **FIXED** (404 for no preferences) - Was 500, fixed exception handling
- [ ] PUT /api/v1/Users/<USER>/preferences
- [ ] GET /api/v1/Users/<USER>
- [ ] GET /api/v1/Users (list users)
- [ ] POST /api/v1/Users/<USER>
- [ ] GET /api/v1/Users/<USER>

### Sites Endpoints 🔄
- [x] GET /api/v1/Sites - **WORKING** (200 OK, empty result)
- [ ] POST /api/v1/Sites
- [ ] GET /api/v1/Sites/{id}
- [ ] PUT /api/v1/Sites/{id}
- [ ] DELETE /api/v1/Sites/{id}

### Pages Endpoints 🔄
- [x] GET /api/v1/Sites/{siteId}/Pages - **FIXED** (404 for no pages) - Was 500, fixed EF column mapping
- [ ] POST /api/v1/Sites/{siteId}/Pages
- [ ] GET /api/v1/Sites/{siteId}/Pages/{id}
- [ ] PUT /api/v1/Sites/{siteId}/Pages/{id}
- [ ] DELETE /api/v1/Sites/{siteId}/Pages/{id}

### Addons Endpoints 🔄
- [x] GET /api/v1/Addons/global - **WORKING** (200 OK, empty result)
- [x] GET /api/v1/Addons/purchases - **WORKING** (200 OK, empty result)

### Auth Endpoints 🔄
- [x] GET /api/v1/Auth/me - **WORKING** (200 OK, returns user profile)

### Admin Endpoints 🔄
- [ ] GET /api/v1/Admin/users
- [ ] GET /api/v1/Admin/system-health
- [ ] GET /api/v1/Admin/configuration/{key}
- [ ] PUT /api/v1/Admin/configuration

### Payments Endpoints 🔄
- [ ] POST /api/v1/Payments/create-payment-intent
- [ ] GET /api/v1/Payments/user-payments

## Issues Found
1. **AddonDefinitions** - Entity Framework model mismatch (FIXED)
   - Missing navigation properties causing SQL errors
   - Shadow property conflicts

## Testing Notes
- Using test token: Valid JWT for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a
- Testing against: http://localhost:5000
- Current user is non-admin, so admin endpoints should return 403
