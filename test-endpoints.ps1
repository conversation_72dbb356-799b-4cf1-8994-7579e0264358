# VelocityPlatform API Endpoint Testing Script
Write-Host "🧪 Testing VelocityPlatform API Endpoints" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$headers = @{ "Content-Type" = "application/json" }

# Test 1: Health Check
Write-Host "`n1. Testing Health Endpoint..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/health" -Method Get -TimeoutSec 5
    Write-Host "✅ Health endpoint working" -ForegroundColor Green
    Write-Host "Response: $response" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Health endpoint failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Swagger Documentation
Write-Host "`n2. Testing Swagger Documentation..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/swagger" -Method Get -TimeoutSec 5
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Swagger documentation accessible" -ForegroundColor Green
    }
} catch {
    Write-Host "❌ Swagger documentation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: API Root
Write-Host "`n3. Testing API Root..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/" -Method Get -TimeoutSec 5
    Write-Host "✅ API root accessible" -ForegroundColor Green
    Write-Host "Response: $response" -ForegroundColor Cyan
} catch {
    Write-Host "❌ API root failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: Auth Registration (should work without authentication)
Write-Host "`n4. Testing Auth Registration..." -ForegroundColor Yellow
try {
    $registerData = @{
        Email = "test$(Get-Random)@example.com"
        Password = "TestPassword123!"
        FirstName = "Test"
        LastName = "User"
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Auth/register" -Method Post -Body $registerData -Headers $headers -TimeoutSec 10
    Write-Host "✅ Auth registration working" -ForegroundColor Green
    Write-Host "Response: $($response | ConvertTo-Json -Depth 2)" -ForegroundColor Cyan
    
    # Store token for further tests
    $global:authToken = $response.data.token
    $global:authHeaders = @{ 
        "Content-Type" = "application/json"
        "Authorization" = "Bearer $global:authToken"
    }
} catch {
    Write-Host "❌ Auth registration failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Component Templates (requires authentication)
Write-Host "`n5. Testing Component Templates..." -ForegroundColor Yellow
try {
    if ($global:authHeaders) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/templates" -Method Get -Headers $global:authHeaders -TimeoutSec 10
        Write-Host "✅ Component templates working" -ForegroundColor Green
        Write-Host "Found $($response.data.Count) templates" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no auth token available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Component templates failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Component Categories
Write-Host "`n6. Testing Component Categories..." -ForegroundColor Yellow
try {
    if ($global:authHeaders) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/categories" -Method Get -Headers $global:authHeaders -TimeoutSec 10
        Write-Host "✅ Component categories working" -ForegroundColor Green
        Write-Host "Found $($response.data.Count) categories" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no auth token available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Component categories failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: File Categories
Write-Host "`n7. Testing File Categories..." -ForegroundColor Yellow
try {
    if ($global:authHeaders) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Files/categories" -Method Get -Headers $global:authHeaders -TimeoutSec 10
        Write-Host "✅ File categories working" -ForegroundColor Green
        Write-Host "Found $($response.data.Count) categories" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no auth token available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ File categories failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Media Files
Write-Host "`n8. Testing Media Files..." -ForegroundColor Yellow
try {
    if ($global:authHeaders) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Files/media" -Method Get -Headers $global:authHeaders -TimeoutSec 10
        Write-Host "✅ Media files working" -ForegroundColor Green
        Write-Host "Found $($response.data.data.Count) media files" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no auth token available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Media files failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Addon Builder Templates
Write-Host "`n9. Testing Addon Builder Templates..." -ForegroundColor Yellow
try {
    if ($global:authHeaders) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/addon-builder/templates" -Method Get -Headers $global:authHeaders -TimeoutSec 10
        Write-Host "✅ Addon builder templates working" -ForegroundColor Green
        Write-Host "Found $($response.data.Count) addon templates" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no auth token available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Addon builder templates failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 10: Addon Marketplace Categories
Write-Host "`n10. Testing Addon Marketplace Categories..." -ForegroundColor Yellow
try {
    if ($global:authHeaders) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/addon-builder/marketplace/categories" -Method Get -Headers $global:authHeaders -TimeoutSec 10
        Write-Host "✅ Addon marketplace categories working" -ForegroundColor Green
        Write-Host "Found $($response.data.Count) marketplace categories" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no auth token available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Addon marketplace categories failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 Endpoint testing completed!" -ForegroundColor Green
Write-Host "Check the results above to see which endpoints are working." -ForegroundColor Cyan
