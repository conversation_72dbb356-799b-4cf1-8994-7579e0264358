2025-06-10 17:21:01.243 +01:00 [ERR] Failed executing DbCommand (22ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
CREATE TABLE "AspNetRoles" (
    "Id" uuid NOT NULL,
    "Name" character varying(256),
    "NormalizedName" character varying(256),
    "ConcurrencyStamp" text,
    CONSTRAINT "PK_AspNetRoles" PRIMARY KEY ("Id")
);
2025-06-10 17:21:01.301 +01:00 [ERR] An error occurred while migrating the database
Npgsql.PostgresException (0x80004005): 42P07: relation "AspNetRoles" already exists
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 231
  Exception data:
    Severity: ERROR
    SqlState: 42P07
    MessageText: relation "AspNetRoles" already exists
    File: heap.c
    Line: 1161
    Routine: heap_create_with_catalog
2025-06-10 17:21:01.321 +01:00 [INF] Velocity Platform API starting up...
2025-06-10 17:21:01.376 +01:00 [INF] Now listening on: http://localhost:5000
2025-06-10 17:21:01.379 +01:00 [INF] Application started. Press Ctrl+C to shut down.
2025-06-10 17:21:01.381 +01:00 [INF] Hosting environment: Production
2025-06-10 17:21:01.382 +01:00 [INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish
2025-06-10 17:21:54.558 +01:00 [WRN] Failed to determine the https port for redirect.
2025-06-10 17:21:54.634 +01:00 [ERR] Error in AuditLoggingMiddleware
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method44(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
2025-06-10 17:21:54.645 +01:00 [ERR] An unhandled exception has occurred
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.GetCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method44(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
   at VelocityPlatform.API.Middleware.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\ExceptionHandlingMiddleware.cs:line 25
2025-06-10 17:23:37.298 +01:00 [ERR] Error in AuditLoggingMiddleware
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method52(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
2025-06-10 17:23:37.306 +01:00 [ERR] An unhandled exception has occurred
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method52(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
   at VelocityPlatform.API.Middleware.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\ExceptionHandlingMiddleware.cs:line 25
2025-06-10 17:23:49.926 +01:00 [ERR] Error in AuditLoggingMiddleware
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method52(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
2025-06-10 17:23:49.932 +01:00 [ERR] An unhandled exception has occurred
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method52(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
   at VelocityPlatform.API.Middleware.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\ExceptionHandlingMiddleware.cs:line 25
2025-06-10 17:24:04.998 +01:00 [ERR] Error in AuditLoggingMiddleware
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method60(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
2025-06-10 17:24:05.006 +01:00 [ERR] An unhandled exception has occurred
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Security.JwtTokenService' while attempting to activate 'VelocityPlatform.Business.Services.AuthService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method60(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.AuditLoggingMiddleware.InvokeAsync(HttpContext context, VelocityPlatformDbContext dbContext) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuditLoggingMiddleware.cs:line 36
   at VelocityPlatform.API.Middleware.ExceptionHandlingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\ExceptionHandlingMiddleware.cs:line 25
[2025-06-10 17:26:35.781 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 17:26:37.720 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 17:26:37.881 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 17:26:37.954 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:26:37.957 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:26:37.958 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:26:37.960 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:27:20.688 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:27:20.695 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:27:21.044 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:27:21.048 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:27:32.646 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:27:32.649 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:27:32.788 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:27:32.791 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:28:37.673 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:28:37.677 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:28:37.958 +01:00 ERR] Failed executing DbCommand (25ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?', @p10='?' (DbType = Boolean), @p11='?' (DbType = Boolean), @p12='?' (DbType = Int32), @p13='?', @p14='?' (DbType = Boolean), @p15='?' (DbType = Boolean), @p16='?' (DbType = Boolean), @p17='?' (DbType = DateTime), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = DateTime), @p21='?' (DbType = Boolean), @p22='?' (DbType = DateTime), @p23='?', @p24='?', @p25='?', @p26='?' (DbType = Binary), @p27='?', @p28='?' (DbType = Boolean), @p29='?', @p30='?' (DbType = Object), @p31='?', @p32='?' (DbType = DateTime), @p33='?' (DbType = Int32), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = Boolean), @p37='?' (DbType = DateTime), @p38='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AspNetUsers" ("Id", "AccessFailedCount", "AnonymizedDate", "ApiKey", "AvatarUrl", "ConcurrencyStamp", "ConfirmationToken", "ConfirmationTokenExpiry", "CreatedAt", "Email", "EmailConfirmed", "EmailVerified", "FailedLoginAttempts", "FirstName", "IsActive", "IsAnonymized", "IsLockedOut", "LastLoginAt", "LastName", "LastPasswordChange", "LockedUntil", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PasswordSalt", "PhoneNumber", "PhoneNumberConfirmed", "PreferencesJson", "ProfileData", "ResetToken", "ResetTokenExpiry", "Role", "SecurityStamp", "TenantId", "TwoFactorEnabled", "UpdatedAt", "UserName")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38); <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 17:28:37.980 +01:00 ERR] An exception occurred in the database while saving changes for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23502: null value in column "PasswordSalt" of relation "AspNetUsers" violates not-null constraint

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23502
    MessageText: null value in column "PasswordSalt" of relation "AspNetUsers" violates not-null constraint
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ColumnName: PasswordSalt
    File: execMain.c
    Line: 1988
    Routine: ExecConstraints
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) <s:Microsoft.EntityFrameworkCore.Update>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23502: null value in column "PasswordSalt" of relation "AspNetUsers" violates not-null constraint

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23502
    MessageText: null value in column "PasswordSalt" of relation "AspNetUsers" violates not-null constraint
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ColumnName: PasswordSalt
    File: execMain.c
    Line: 1988
    Routine: ExecConstraints
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-06-10 17:28:38.002 +01:00 ERR] An unhandled exception occurred: An error occurred while saving the entity changes. See the inner exception for details. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23502: null value in column "PasswordSalt" of relation "AspNetUsers" violates not-null constraint

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23502
    MessageText: null value in column "PasswordSalt" of relation "AspNetUsers" violates not-null constraint
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ColumnName: PasswordSalt
    File: execMain.c
    Line: 1988
    Routine: ExecConstraints
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.EntityFrameworkCore.UserStore`9.CreateAsync(TUser user, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(TUser user)
   at Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(TUser user, String password)
   at VelocityPlatform.Business.Services.AuthService.RegisterAsync(RegisterRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 117
   at VelocityPlatform.API.Controllers.AuthController.Register(RegisterRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 152
   at lambda_method359(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 17:30:08.413 +01:00 ERR] An unhandled exception occurred: User ID not found or invalid <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: User ID not found or invalid
   at VelocityPlatform.API.Controllers.BaseController.GetCurrentUserId() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\BaseController.cs:line 63
   at VelocityPlatform.API.Controllers.SitesController.GetSites(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs:line 36
   at lambda_method559(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 17:33:57.124 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:33:57.127 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:33:57.458 +01:00 ERR] Failed executing DbCommand (41ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?', @p10='?' (DbType = Boolean), @p11='?' (DbType = Boolean), @p12='?' (DbType = Int32), @p13='?', @p14='?' (DbType = Boolean), @p15='?' (DbType = Boolean), @p16='?' (DbType = Boolean), @p17='?' (DbType = DateTime), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = DateTime), @p21='?' (DbType = Boolean), @p22='?' (DbType = DateTime), @p23='?', @p24='?', @p25='?', @p26='?' (DbType = Binary), @p27='?', @p28='?' (DbType = Boolean), @p29='?', @p30='?' (DbType = Object), @p31='?', @p32='?' (DbType = DateTime), @p33='?' (DbType = Int32), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = Boolean), @p37='?' (DbType = DateTime), @p38='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AspNetUsers" ("Id", "AccessFailedCount", "AnonymizedDate", "ApiKey", "AvatarUrl", "ConcurrencyStamp", "ConfirmationToken", "ConfirmationTokenExpiry", "CreatedAt", "Email", "EmailConfirmed", "EmailVerified", "FailedLoginAttempts", "FirstName", "IsActive", "IsAnonymized", "IsLockedOut", "LastLoginAt", "LastName", "LastPasswordChange", "LockedUntil", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PasswordSalt", "PhoneNumber", "PhoneNumberConfirmed", "PreferencesJson", "ProfileData", "ResetToken", "ResetTokenExpiry", "Role", "SecurityStamp", "TenantId", "TwoFactorEnabled", "UpdatedAt", "UserName")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38); <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 17:33:57.468 +01:00 ERR] An exception occurred in the database while saving changes for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23503: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23503
    MessageText: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ConstraintName: FK_AspNetUsers_Tenants_TenantId
    File: ri_triggers.c
    Line: 2610
    Routine: ri_ReportViolation
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) <s:Microsoft.EntityFrameworkCore.Update>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23503: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23503
    MessageText: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ConstraintName: FK_AspNetUsers_Tenants_TenantId
    File: ri_triggers.c
    Line: 2610
    Routine: ri_ReportViolation
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-06-10 17:33:57.497 +01:00 ERR] An unhandled exception occurred: An error occurred while saving the entity changes. See the inner exception for details. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23503: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23503
    MessageText: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ConstraintName: FK_AspNetUsers_Tenants_TenantId
    File: ri_triggers.c
    Line: 2610
    Routine: ri_ReportViolation
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.EntityFrameworkCore.UserStore`9.CreateAsync(TUser user, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(TUser user)
   at Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(TUser user, String password)
   at VelocityPlatform.Business.Services.AuthService.RegisterAsync(RegisterRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 117
   at VelocityPlatform.API.Controllers.AuthController.Register(RegisterRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 152
   at lambda_method359(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 17:34:49.711 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:34:49.713 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:34:49.797 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:34:49.799 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:35:09.096 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:35:09.098 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:35:09.183 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:35:09.185 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:15:23.711 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:15:25.498 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:15:25.673 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:15:25.743 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:15:25.746 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:15:25.748 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:15:25.749 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:16:04.875 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:16:04.880 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:16:05.009 +01:00 ERR] Failed executing DbCommand (27ms) [Parameters=[@__request_Email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId", a."TwoFactorEnabled", a."UpdatedAt", a."UserName", t."Id", t."CreatedAt", t."CreatedByUserId", t."Domain", t."IsActive", t."IsolationEnforcedDate", t."IsolationLevel", t."LastModifiedByUserId", t."MaxSites", t."MaxUsers", t."Name", t."Slug", t."Status", t."SubscriptionPlan", t."TenantId", t."UpdatedAt"
FROM "AspNetUsers" AS a
INNER JOIN "Tenants" AS t ON a."TenantId" = t."Id"
WHERE a."Email" = @__request_Email_0
LIMIT 1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:16:05.026 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:16:05.043 +01:00 ERR] An unhandled exception occurred: 42703: column t.CreatedByUserId does not exist

POSITION: 739 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 48
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 118
   at lambda_method55(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:16:15.402 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:16:15.404 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:16:15.463 +01:00 ERR] Failed executing DbCommand (19ms) [Parameters=[@__request_Email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId", a."TwoFactorEnabled", a."UpdatedAt", a."UserName", t."Id", t."CreatedAt", t."CreatedByUserId", t."Domain", t."IsActive", t."IsolationEnforcedDate", t."IsolationLevel", t."LastModifiedByUserId", t."MaxSites", t."MaxUsers", t."Name", t."Slug", t."Status", t."SubscriptionPlan", t."TenantId", t."UpdatedAt"
FROM "AspNetUsers" AS a
INNER JOIN "Tenants" AS t ON a."TenantId" = t."Id"
WHERE a."Email" = @__request_Email_0
LIMIT 1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:16:15.468 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:16:15.480 +01:00 ERR] An unhandled exception occurred: 42703: column t.CreatedByUserId does not exist

POSITION: 739 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 48
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 118
   at lambda_method55(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:16:25.783 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:16:25.786 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:16:25.807 +01:00 ERR] Failed executing DbCommand (18ms) [Parameters=[@__request_Email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId", a."TwoFactorEnabled", a."UpdatedAt", a."UserName", t."Id", t."CreatedAt", t."CreatedByUserId", t."Domain", t."IsActive", t."IsolationEnforcedDate", t."IsolationLevel", t."LastModifiedByUserId", t."MaxSites", t."MaxUsers", t."Name", t."Slug", t."Status", t."SubscriptionPlan", t."TenantId", t."UpdatedAt"
FROM "AspNetUsers" AS a
INNER JOIN "Tenants" AS t ON a."TenantId" = t."Id"
WHERE a."Email" = @__request_Email_0
LIMIT 1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:16:25.812 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:16:25.824 +01:00 ERR] An unhandled exception occurred: 42703: column t.CreatedByUserId does not exist

POSITION: 739 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column t.CreatedByUserId does not exist

POSITION: 739
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 48
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 118
   at lambda_method55(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.CreatedByUserId does not exist
    Position: 739
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:19:33.977 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:19:35.772 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:19:35.908 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:19:35.975 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:19:35.977 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:19:35.979 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:19:35.980 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:20:14.733 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:20:14.738 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:20:14.860 +01:00 ERR] Failed executing DbCommand (29ms) [Parameters=[@__request_Email_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId", a."TwoFactorEnabled", a."UpdatedAt", a."UserName", t."Id", t."CreatedAt", t."IsActive", t."IsolationEnforcedDate", t."IsolationLevel", t."MaxSites", t."MaxUsers", t."Name", t."Slug", t."Status", t."SubscriptionPlan", t."TenantId", t."UpdatedAt"
FROM "AspNetUsers" AS a
INNER JOIN "Tenants" AS t ON a."TenantId" = t."Id"
WHERE a."Email" = @__request_Email_0
LIMIT 1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:20:14.877 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column t.Status does not exist

POSITION: 848
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.Status does not exist
    Position: 848
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column t.Status does not exist

POSITION: 848
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.Status does not exist
    Position: 848
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:20:14.894 +01:00 ERR] An unhandled exception occurred: 42703: column t.Status does not exist

POSITION: 848 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column t.Status does not exist

POSITION: 848
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 48
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 118
   at lambda_method55(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column t.Status does not exist
    Position: 848
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:21:03.185 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:21:04.997 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:21:05.140 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:21:05.220 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:21:05.223 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:21:05.224 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:21:05.225 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:21:45.233 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:21:45.238 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:21:45.565 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:21:45.568 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:22:01.796 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:22:01.798 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:22:01.920 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:22:01.922 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:22:13.848 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:22:13.852 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:22:13.936 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:22:13.938 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:22:26.874 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:22:52.776 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:23:04.550 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:23:04.554 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:23:04.584 +01:00 ERR] Registration <NAME_EMAIL>: Passwords must have at least one non alphanumeric character., Passwords must have at least one uppercase ('A'-'Z'). <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:23:04.586 +01:00 WRN] Registration <NAME_EMAIL>: Passwords must have at least one non alphanumeric character. Passwords must have at least one uppercase ('A'-'Z'). <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:23:15.105 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:23:15.107 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:23:15.339 +01:00 ERR] Failed executing DbCommand (26ms) [Parameters=[@p0='?' (DbType = Guid), @p1='?' (DbType = Int32), @p2='?' (DbType = DateTime), @p3='?', @p4='?', @p5='?', @p6='?', @p7='?' (DbType = DateTime), @p8='?' (DbType = DateTime), @p9='?', @p10='?' (DbType = Boolean), @p11='?' (DbType = Boolean), @p12='?' (DbType = Int32), @p13='?', @p14='?' (DbType = Boolean), @p15='?' (DbType = Boolean), @p16='?' (DbType = Boolean), @p17='?' (DbType = DateTime), @p18='?', @p19='?' (DbType = DateTime), @p20='?' (DbType = DateTime), @p21='?' (DbType = Boolean), @p22='?' (DbType = DateTime), @p23='?', @p24='?', @p25='?', @p26='?' (DbType = Binary), @p27='?', @p28='?' (DbType = Boolean), @p29='?', @p30='?' (DbType = Object), @p31='?', @p32='?' (DbType = DateTime), @p33='?' (DbType = Int32), @p34='?', @p35='?' (DbType = Guid), @p36='?' (DbType = Boolean), @p37='?' (DbType = DateTime), @p38='?'], CommandType='"Text"', CommandTimeout='30']
INSERT INTO "AspNetUsers" ("Id", "AccessFailedCount", "AnonymizedDate", "ApiKey", "AvatarUrl", "ConcurrencyStamp", "ConfirmationToken", "ConfirmationTokenExpiry", "CreatedAt", "Email", "EmailConfirmed", "EmailVerified", "FailedLoginAttempts", "FirstName", "IsActive", "IsAnonymized", "IsLockedOut", "LastLoginAt", "LastName", "LastPasswordChange", "LockedUntil", "LockoutEnabled", "LockoutEnd", "NormalizedEmail", "NormalizedUserName", "PasswordHash", "PasswordSalt", "PhoneNumber", "PhoneNumberConfirmed", "PreferencesJson", "ProfileData", "ResetToken", "ResetTokenExpiry", "Role", "SecurityStamp", "TenantId", "TwoFactorEnabled", "UpdatedAt", "UserName")
VALUES (@p0, @p1, @p2, @p3, @p4, @p5, @p6, @p7, @p8, @p9, @p10, @p11, @p12, @p13, @p14, @p15, @p16, @p17, @p18, @p19, @p20, @p21, @p22, @p23, @p24, @p25, @p26, @p27, @p28, @p29, @p30, @p31, @p32, @p33, @p34, @p35, @p36, @p37, @p38); <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:23:15.354 +01:00 ERR] An exception occurred in the database while saving changes for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23503: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23503
    MessageText: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ConstraintName: FK_AspNetUsers_Tenants_TenantId
    File: ri_triggers.c
    Line: 2610
    Routine: ri_ReportViolation
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken) <s:Microsoft.EntityFrameworkCore.Update>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23503: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23503
    MessageText: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ConstraintName: FK_AspNetUsers_Tenants_TenantId
    File: ri_triggers.c
    Line: 2610
    Routine: ri_ReportViolation
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
[2025-06-10 22:23:15.371 +01:00 ERR] An unhandled exception occurred: An error occurred while saving the entity changes. See the inner exception for details. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Npgsql.PostgresException (0x80004005): 23503: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"

DETAIL: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
  Exception data:
    Severity: ERROR
    SqlState: 23503
    MessageText: insert or update on table "AspNetUsers" violates foreign key constraint "FK_AspNetUsers_Tenants_TenantId"
    Detail: Detail redacted as it may contain sensitive data. Specify 'Include Error Detail' in the connection string to include this information.
    SchemaName: public
    TableName: AspNetUsers
    ConstraintName: FK_AspNetUsers_Tenants_TenantId
    File: ri_triggers.c
    Line: 2610
    Routine: ri_ReportViolation
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.EntityFrameworkCore.UserStore`9.CreateAsync(TUser user, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(TUser user)
   at Microsoft.AspNetCore.Identity.UserManager`1.CreateAsync(TUser user, String password)
   at VelocityPlatform.Business.Services.AuthService.RegisterAsync(RegisterRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 120
   at VelocityPlatform.API.Controllers.AuthController.Register(RegisterRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 152
   at lambda_method507(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 22:24:43.546 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:24:45.368 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:24:45.513 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:24:45.583 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:24:45.586 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:24:45.588 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:24:45.589 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:25:25.503 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:25:25.508 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:25:25.964 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:25:25.968 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for New. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUY1iCT6OvZT/B9QJpCLIeL2M/bmHoUxAq5hf9SosF27oGyDWXe8rbeb4gUHz1vqVOpS2u33ougFN/9a1f0M+72wV/nW5zaQWIzcDFnAJ10RVTRaE4ffJJUXb/GFO1L9z1AAuFfJMUl0NFl1UMxd4ucDDvoLmajIpUDxIkHqPKvQw/zERw+I4F/XlKgg4NJ550LJgfufhJoZ/5TAs+d0rmZQK9QeZFqe5SU9h8ksGSMAQA== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-10 22:25:26.079 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:25:45.192 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:26:26.476 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:26:26.480 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:26:26.578 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:26:26.580 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:27:53.527 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:27:53.530 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:27:53.636 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:27:53.638 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:28:02.814 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:28:02.816 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:28:02.899 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:28:02.901 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:28:18.163 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:28:18.165 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:28:18.250 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 22:28:18.252 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 22:29:15.031 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-10 22:29:15.067 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-10 22:29:24.596 +01:00 ERR] Failed executing DbCommand (21ms) [Parameters=[@__tenantId_0='?' (DbType = Guid), @__currentUserId_1='?' (DbType = Guid), @__p_3='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."AnalyticsSettings", s."ApprovalStatus", s."Configuration", s."CreatedAt", s."CreatedByUserId", s."CurrentVersionId", s."Description", s."Domain", s."IsActive", s."LastCompilationDate", s."LastCompilationStatus", s."LastDeploymentDate", s."LastModifiedByUserId", s."Name", s."OwnerId", s."PageHierarchy", s."PublishedAt", s."ReviewRequestedAt", s."ReviewerId", s."ScheduledPublishAt", s."SeoSettings", s."Status", s."Subdomain", s."TenantId", s."UpdatedAt"
FROM "Sites" AS s
WHERE s."TenantId" = @__tenantId_0 AND s."IsActive" AND s."OwnerId" = @__currentUserId_1
ORDER BY s."CreatedAt" DESC
LIMIT @__p_3 OFFSET @__p_2 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:29:24.609 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column s.ApprovalStatus does not exist

POSITION: 39
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.ApprovalStatus does not exist
    Position: 39
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column s.ApprovalStatus does not exist

POSITION: 39
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.ApprovalStatus does not exist
    Position: 39
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:29:24.622 +01:00 ERR] An unhandled exception occurred: 42703: column s.ApprovalStatus does not exist

POSITION: 39 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column s.ApprovalStatus does not exist

POSITION: 39
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SiteService.GetSitesAsync(Guid tenantId, Guid currentUserId, Boolean isPlatformAdmin, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SiteService.cs:line 122
   at VelocityPlatform.API.Controllers.SitesController.GetSites(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs:line 39
   at lambda_method775(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.ApprovalStatus does not exist
    Position: 39
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:30:37.139 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:30:38.984 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:30:39.162 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:30:39.231 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:30:39.234 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:30:39.235 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:30:39.236 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:31:20.117 +01:00 ERR] Failed executing DbCommand (21ms) [Parameters=[@__tenantId_0='?' (DbType = Guid), @__currentUserId_1='?' (DbType = Guid), @__p_3='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."AnalyticsSettings", s."Configuration", s."CreatedAt", s."CurrentVersionId", s."Description", s."Domain", s."IsActive", s."Name", s."OwnerId", s."PublishedAt", s."ReviewerId", s."SeoSettings", s."Status", s."Subdomain", s."TenantId", s."UpdatedAt"
FROM "Sites" AS s
WHERE s."TenantId" = @__tenantId_0 AND s."IsActive" AND s."OwnerId" = @__currentUserId_1
ORDER BY s."CreatedAt" DESC
LIMIT @__p_3 OFFSET @__p_2 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:31:20.134 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column s.ReviewerId does not exist

POSITION: 178
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.ReviewerId does not exist
    Position: 178
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column s.ReviewerId does not exist

POSITION: 178
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.ReviewerId does not exist
    Position: 178
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:31:20.151 +01:00 ERR] An unhandled exception occurred: 42703: column s.ReviewerId does not exist

POSITION: 178 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column s.ReviewerId does not exist

POSITION: 178
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SiteService.GetSitesAsync(Guid tenantId, Guid currentUserId, Boolean isPlatformAdmin, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SiteService.cs:line 122
   at VelocityPlatform.API.Controllers.SitesController.GetSites(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SitesController.cs:line 39
   at lambda_method55(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.ReviewerId does not exist
    Position: 178
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:32:43.623 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:32:45.408 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:32:45.589 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:32:45.657 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:32:45.660 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:32:45.662 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:32:45.663 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:36:47.576 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:36:47.660 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:36:47.662 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:36:47.729 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:37:06.675 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:37:06.705 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:37:06.708 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:37:06.749 +01:00 ERR] Failed executing DbCommand (22ms) [Parameters=[@__currentUserId_Value_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."AddonType", t."ApprovedAt", t."ApprovedBy", t."BillingType", t."Category", t."CreatedAt", t."CreatedByUserId", t."CreatorId", t."Currency", t."CurrentVersionId", t."DeletedAt", t."Description", t."DownloadCount", t."GlobalAvailabilityDate", t."IsActive", t."IsDeleted", t."IsGloballyAvailable", t."IsPublic", t."LastModifiedByUserId", t."Name", t."Price", t."RatingAverage", t."RatingCount", t."RejectionReason", t."Status", t."Tags", t."TenantId", t."UpdatedAt", a0."Id", a0."AccessFailedCount", a0."AnonymizedDate", a0."ApiKey", a0."AvatarUrl", a0."ConcurrencyStamp", a0."ConfirmationToken", a0."ConfirmationTokenExpiry", a0."CreatedAt", a0."Email", a0."EmailConfirmed", a0."EmailVerified", a0."FailedLoginAttempts", a0."FirstName", a0."IsActive", a0."IsAnonymized", a0."IsLockedOut", a0."LastLoginAt", a0."LastName", a0."LastPasswordChange", a0."LockedUntil", a0."LockoutEnabled", a0."LockoutEnd", a0."NormalizedEmail", a0."NormalizedUserName", a0."PasswordHash", a0."PasswordSalt", a0."PhoneNumber", a0."PhoneNumberConfirmed", a0."PreferencesJson", a0."ProfileData", a0."ResetToken", a0."ResetTokenExpiry", a0."Role", a0."SecurityStamp", a0."TenantId", a0."TwoFactorEnabled", a0."UpdatedAt", a0."UserName"
FROM (
    SELECT a."Id", a."AddonType", a."ApprovedAt", a."ApprovedBy", a."BillingType", a."Category", a."CreatedAt", a."CreatedByUserId", a."CreatorId", a."Currency", a."CurrentVersionId", a."DeletedAt", a."Description", a."DownloadCount", a."GlobalAvailabilityDate", a."IsActive", a."IsDeleted", a."IsGloballyAvailable", a."IsPublic", a."LastModifiedByUserId", a."Name", a."Price", a."RatingAverage", a."RatingCount", a."RejectionReason", a."Status", a."Tags", a."TenantId", a."UpdatedAt"
    FROM "AddonDefinitions" AS a
    WHERE NOT (a."IsDeleted") AND (a."Status" = 3 OR a."CreatorId" = @__currentUserId_Value_0)
    LIMIT @__p_2 OFFSET @__p_1
) AS t
INNER JOIN "AspNetUsers" AS a0 ON t."CreatorId" = a0."Id" <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:37:06.762 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column a.CreatedByUserId does not exist

POSITION: 1350
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.CreatedByUserId does not exist
    Position: 1350
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column a.CreatedByUserId does not exist

POSITION: 1350
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.CreatedByUserId does not exist
    Position: 1350
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:38:22.337 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:38:24.147 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:38:24.290 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:38:24.360 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:38:24.363 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:38:24.364 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:38:24.365 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:39:06.415 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:40:10.514 +01:00 INF] Getting all predefined snippets <s:VelocityPlatform.Business.Services.PredefinedSnippetService>
[2025-06-10 22:40:10.611 +01:00 ERR] Failed executing DbCommand (24ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."Category", p."CreatedAt", p."CreatedBy", p."CreatedByUserId", p."CreatorId", p."CurrentVersionId", p."Description", p."IsActive", p."IsPublic", p."LastModifiedByUserId", p."Name", p."PreviewData", p."Tags", p."TenantId", p."UpdatedAt", p."UsageCount"
FROM "PredefinedSnippets" AS p <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:40:10.626 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column p.CreatedByUserId does not exist

POSITION: 60
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CreatedByUserId does not exist
    Position: 60
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column p.CreatedByUserId does not exist

POSITION: 60
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CreatedByUserId does not exist
    Position: 60
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:40:10.642 +01:00 ERR] An unhandled exception occurred: 42703: column p.CreatedByUserId does not exist

POSITION: 60 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column p.CreatedByUserId does not exist

POSITION: 60
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.PredefinedSnippetService.GetPredefinedSnippetsAsync() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\PredefinedSnippetService.cs:line 26
   at VelocityPlatform.API.Controllers.PredefinedSnippetsController.GetPredefinedSnippets() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\PredefinedSnippetsController.cs:line 52
   at lambda_method63(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CreatedByUserId does not exist
    Position: 60
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:41:47.661 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:41:49.443 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:41:49.622 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:41:49.689 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:41:49.693 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:41:49.694 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:41:49.695 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:42:28.670 +01:00 INF] Getting all predefined snippets <s:VelocityPlatform.Business.Services.PredefinedSnippetService>
[2025-06-10 22:42:46.610 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:45:52.243 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:45:54.365 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:45:54.510 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:45:54.578 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:45:54.582 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:45:54.583 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:45:54.584 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:46:33.500 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:49:06.407 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:49:08.279 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:49:08.424 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:49:08.493 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:49:08.495 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:49:08.497 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:49:08.498 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:49:49.698 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:49:49.800 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:49:49.803 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:49:49.852 +01:00 ERR] Failed executing DbCommand (21ms) [Parameters=[@__currentUserId_Value_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AddonType", a."ApprovedAt", a."ApprovedBy", a."BillingType", a."Category", a."CreatedAt", a."CreatorId", a."Currency", a."CurrentVersionId", a."DeletedAt", a."Description", a."DownloadCount", a."GlobalAvailabilityDate", a."IsActive", a."IsDeleted", a."IsGloballyAvailable", a."IsPublic", a."Name", a."Price", a."RatingAverage", a."RatingCount", a."RejectionReason", a."Status", a."Tags", a."TenantId", a."UpdatedAt", a."UserId"
FROM "AddonDefinitions" AS a
WHERE NOT (a."IsDeleted") AND (a."Status" = 3 OR a."CreatorId" = @__currentUserId_Value_0)
LIMIT @__p_2 OFFSET @__p_1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:49:49.865 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column a.UserId does not exist

POSITION: 436
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.UserId does not exist
    Position: 436
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column a.UserId does not exist

POSITION: 436
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.UserId does not exist
    Position: 436
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:56:04.430 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:56:05.129 +01:00 WRN] The foreign key property 'AddonDefinition.UserId1' was created in shadow state because a conflicting property with the simple name 'UserId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core. <s:Microsoft.EntityFrameworkCore.Model.Validation>
[2025-06-10 22:56:06.220 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:56:06.366 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:56:06.432 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:56:06.434 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:56:06.436 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:56:06.437 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:56:45.010 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:56:45.107 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:56:45.111 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:56:45.159 +01:00 ERR] Failed executing DbCommand (21ms) [Parameters=[@__currentUserId_Value_0='?' (DbType = Guid), @__p_2='?' (DbType = Int32), @__p_1='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AddonType", a."ApprovedAt", a."ApprovedBy", a."BillingType", a."Category", a."CreatedAt", a."CreatorId", a."Currency", a."CurrentVersionId", a."DeletedAt", a."Description", a."DownloadCount", a."GlobalAvailabilityDate", a."IsActive", a."IsDeleted", a."IsGloballyAvailable", a."IsPublic", a."Name", a."Price", a."RatingAverage", a."RatingCount", a."RejectionReason", a."Status", a."Tags", a."TenantId", a."UpdatedAt", a."UserId1"
FROM "AddonDefinitions" AS a
WHERE NOT (a."IsDeleted") AND (a."Status" = 3 OR a."CreatorId" = @__currentUserId_Value_0)
LIMIT @__p_2 OFFSET @__p_1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 22:56:45.172 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column a.UserId1 does not exist

POSITION: 436
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.UserId1 does not exist
    Position: 436
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column a.UserId1 does not exist

POSITION: 436
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.UserId1 does not exist
    Position: 436
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 22:58:04.331 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 22:58:06.075 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 22:58:06.224 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 22:58:06.290 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:58:06.293 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:58:06.294 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:58:06.295 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 22:58:47.261 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:58:47.362 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:58:47.366 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 22:58:47.408 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:59:08.750 +01:00 INF] Getting addon definition "123e4567-e89b-12d3-a456-************" for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 22:59:08.816 +01:00 WRN] Addon definition "123e4567-e89b-12d3-a456-************" not found <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:03:43.530 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-10 23:03:57.178 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-10 23:03:57.206 +01:00 ERR] Error retrieving user preferences. <s:VelocityPlatform.API.Controllers.UsersController>
VelocityPlatform.Business.Exceptions.ResourceNotFoundException: User with ID e0d5169b-b25a-4cfb-a4c1-95c764e7f69a not found or has no preferences.
   at VelocityPlatform.Business.Services.UserService.GetUserPreferencesAsync(Guid userId, Guid currentUserId) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\UserService.cs:line 456
   at VelocityPlatform.API.Controllers.UsersController.GetUserPreferences(Guid id) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\UsersController.cs:line 369
[2025-06-10 23:08:09.114 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:08:11.166 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:08:11.313 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:08:11.382 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:08:11.386 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:08:11.387 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:08:11.388 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:08:58.377 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-10 23:10:18.754 +01:00 INF] Getting page hierarchy for site "123e4567-e89b-12d3-a456-************", tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.PagesService>
[2025-06-10 23:10:18.793 +01:00 ERR] Failed executing DbCommand (22ms) [Parameters=[@__siteId_0='?' (DbType = Guid), @__tenantId_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."CreatedAt", p."CurrentPageVersionId", p."Description", p."IsActive", p."IsHomepage", p."IsPublished", p."LayoutConfiguration", p."Name", p."Order", p."Path", p."PublishedDate", p."SiteId", p."Slug", p."TenantId", p."Title", p."UpdatedAt"
FROM "Pages" AS p
WHERE p."SiteId" = @__siteId_0 AND p."TenantId" = @__tenantId_1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 23:10:18.807 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column p.CurrentPageVersionId does not exist

POSITION: 31
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CurrentPageVersionId does not exist
    Position: 31
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column p.CurrentPageVersionId does not exist

POSITION: 31
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CurrentPageVersionId does not exist
    Position: 31
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:10:18.821 +01:00 ERR] Error retrieving page hierarchy for site "123e4567-e89b-12d3-a456-************" <s:VelocityPlatform.API.Controllers.PagesController>
Npgsql.PostgresException (0x80004005): 42703: column p.CurrentPageVersionId does not exist

POSITION: 31
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.PagesService.GetPageHierarchyAsync(Guid siteId, Guid tenantId) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\PagesService.cs:line 169
   at VelocityPlatform.API.Controllers.PagesController.GetPageHierarchy(Guid siteId) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\PagesController.cs:line 34
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CurrentPageVersionId does not exist
    Position: 31
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:12:00.810 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:12:02.713 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:12:02.852 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:12:02.918 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:12:02.922 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:12:02.923 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:12:02.924 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:12:42.188 +01:00 INF] Getting page hierarchy for site "123e4567-e89b-12d3-a456-************", tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.PagesService>
[2025-06-10 23:12:42.278 +01:00 ERR] Failed executing DbCommand (27ms) [Parameters=[@__siteId_0='?' (DbType = Guid), @__tenantId_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT p."Id", p."CreatedAt", p."CurrentPageVersionId", p."Description", p."IsActive", p."IsHomepage", p."IsPublished", p."LayoutConfiguration", p."Name", p."Order", p."Path", p."PublishedDate", p."SiteId", p."Slug", p."TenantId", p."Title", p."UpdatedAt"
FROM "Pages" AS p
WHERE p."SiteId" = @__siteId_0 AND p."TenantId" = @__tenantId_1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 23:12:42.293 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column p.CurrentPageVersionId does not exist

POSITION: 31
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CurrentPageVersionId does not exist
    Position: 31
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column p.CurrentPageVersionId does not exist

POSITION: 31
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CurrentPageVersionId does not exist
    Position: 31
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:12:42.307 +01:00 ERR] Error retrieving page hierarchy for site "123e4567-e89b-12d3-a456-************" <s:VelocityPlatform.API.Controllers.PagesController>
Npgsql.PostgresException (0x80004005): 42703: column p.CurrentPageVersionId does not exist

POSITION: 31
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.PagesService.GetPageHierarchyAsync(Guid siteId, Guid tenantId) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\PagesService.cs:line 169
   at VelocityPlatform.API.Controllers.PagesController.GetPageHierarchy(Guid siteId) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\PagesController.cs:line 34
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column p.CurrentPageVersionId does not exist
    Position: 31
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:14:13.921 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:14:15.695 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:14:15.837 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:14:15.905 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:14:15.908 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:14:15.909 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:14:15.910 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:14:54.738 +01:00 INF] Getting page hierarchy for site "123e4567-e89b-12d3-a456-************", tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.PagesService>
[2025-06-10 23:15:30.609 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:15:30.700 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 23:15:30.703 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 23:15:30.729 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:15:42.953 +01:00 INF] Getting addon definition "123e4567-e89b-12d3-a456-************" for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:15:42.991 +01:00 WRN] Addon definition "123e4567-e89b-12d3-a456-************" not found <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:16:18.542 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:16:18.576 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 23:16:18.578 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 23:16:18.644 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:16:29.697 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:16:29.730 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 23:16:29.732 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-10 23:16:29.757 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-10 23:16:44.030 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:18:50.401 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-10 23:18:50.436 +01:00 ERR] Failed executing DbCommand (23ms) [Parameters=[@__tenantId_0='?' (DbType = Guid), @__userId_Value_1='?' (DbType = Guid)], CommandType='"Text"', CommandTimeout='30']
SELECT count(*)::int
FROM "Payments" AS p
WHERE p."TenantId" = @__tenantId_0 AND p."UserId" = @__userId_Value_1 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 23:18:50.450 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42P01: relation "Payments" does not exist

POSITION: 28
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "Payments" does not exist
    Position: 28
    File: parse_relation.c
    Line: 1452
    Routine: parserOpenTable <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42P01: relation "Payments" does not exist

POSITION: 28
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "Payments" does not exist
    Position: 28
    File: parse_relation.c
    Line: 1452
    Routine: parserOpenTable
[2025-06-10 23:18:50.465 +01:00 ERR] An unhandled exception occurred: 42P01: relation "Payments" does not exist

POSITION: 28 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42P01: relation "Payments" does not exist

POSITION: 28
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.PaymentService.GetPaymentHistoryAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\PaymentService.cs:line 122
   at VelocityPlatform.API.Controllers.PaymentsController.GetPaymentHistory(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\PaymentsController.cs:line 83
   at lambda_method412(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42P01
    MessageText: relation "Payments" does not exist
    Position: 28
    File: parse_relation.c
    Line: 1452
    Routine: parserOpenTable
[2025-06-10 23:26:09.435 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:26:11.223 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:26:11.367 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:26:11.436 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:26:11.438 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:26:11.439 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:26:11.441 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:26:52.661 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-10 23:26:52.667 +01:00 WRN] Payments table not available - returning empty payment history <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-10 23:27:14.920 +01:00 INF] Getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", page 1 <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-10 23:27:15.104 +01:00 ERR] Failed executing DbCommand (21ms) [Parameters=[@__tenantId_0='?' (DbType = Guid), @__userId_Value_1='?' (DbType = Guid), @__p_3='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."CreatedByUserId", t."EndDate", t."IsActive", t."LastModifiedByUserId", t."NextBillingDate", t."StartDate", t."Status", t."SubscriptionPlanId", t."TenantId", t."UpdatedAt", t."UserId", s."Id", s."BillingCycle", s."CreatedAt", s."CreatedByUserId", s."Currency", s."Description", s."Features", s."IsActive", s."IsUsageBased", s."LastModifiedByUserId", s."Name", s."Price", s."TenantId", s."UpdatedAt", t."Id0", t."AccessFailedCount", t."AnonymizedDate", t."ApiKey", t."AvatarUrl", t."ConcurrencyStamp", t."ConfirmationToken", t."ConfirmationTokenExpiry", t."CreatedAt0", t."Email", t."EmailConfirmed", t."EmailVerified", t."FailedLoginAttempts", t."FirstName", t."IsActive0", t."IsAnonymized", t."IsLockedOut", t."LastLoginAt", t."LastName", t."LastPasswordChange", t."LockedUntil", t."LockoutEnabled", t."LockoutEnd", t."NormalizedEmail", t."NormalizedUserName", t."PasswordHash", t."PasswordSalt", t."PhoneNumber", t."PhoneNumberConfirmed", t."PreferencesJson", t."ProfileData", t."ResetToken", t."ResetTokenExpiry", t."Role", t."SecurityStamp", t."TenantId0", t."TwoFactorEnabled", t."UpdatedAt0", t."UserName"
FROM (
    SELECT u."Id", u."CreatedAt", u."CreatedByUserId", u."EndDate", u."IsActive", u."LastModifiedByUserId", u."NextBillingDate", u."StartDate", u."Status", u."SubscriptionPlanId", u."TenantId", u."UpdatedAt", u."UserId", a."Id" AS "Id0", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt" AS "CreatedAt0", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive" AS "IsActive0", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId" AS "TenantId0", a."TwoFactorEnabled", a."UpdatedAt" AS "UpdatedAt0", a."UserName"
    FROM "UserSubscriptions" AS u
    INNER JOIN "AspNetUsers" AS a ON u."UserId" = a."Id"
    WHERE a."TenantId" = @__tenantId_0 AND u."UserId" = @__userId_Value_1
    ORDER BY u."CreatedAt" DESC
    LIMIT @__p_3 OFFSET @__p_2
) AS t
INNER JOIN "SubscriptionPlans" AS s ON t."SubscriptionPlanId" = s."Id"
ORDER BY t."CreatedAt" DESC <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 23:27:15.119 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u.CreatedByUserId does not exist

POSITION: 1188
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.CreatedByUserId does not exist
    Position: 1188
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column u.CreatedByUserId does not exist

POSITION: 1188
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.CreatedByUserId does not exist
    Position: 1188
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:27:15.131 +01:00 ERR] Error getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.SubscriptionService>
Npgsql.PostgresException (0x80004005): 42703: column u.CreatedByUserId does not exist

POSITION: 1188
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 284
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.CreatedByUserId does not exist
    Position: 1188
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:27:15.147 +01:00 ERR] An unhandled exception occurred: 42703: column u.CreatedByUserId does not exist

POSITION: 1188 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column u.CreatedByUserId does not exist

POSITION: 1188
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 284
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptions(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 85
   at lambda_method62(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.CreatedByUserId does not exist
    Position: 1188
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 23:28:38.280 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:28:40.205 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:28:40.349 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:28:40.421 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:28:40.424 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:28:40.426 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:28:40.427 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\publish <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:29:35.508 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:29:35.513 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:29:35.967 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:29:35.971 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:29:36.002 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:29:47.187 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:29:47.188 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:29:47.401 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:29:47.404 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:29:47.406 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
