using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Models.DTOs;
using System.Text.Json; // Added for JsonDocument

namespace VelocityPlatform.Business.Services
{
    public class AddonService : IAddonService
    {
        private readonly VelocityPlatformDbContext _context;
        private readonly ILogger<AddonService> _logger;

        public AddonService(VelocityPlatformDbContext context, ILogger<AddonService> logger)
        {
            _context = context;
            _logger = logger;
        }

        public async Task<AddonInstance> CreateAddonInstanceAsync(AddonInstance instance)
        {
            try
            {
                _logger.LogInformation("Creating addon instance for addon {AddonId}", instance.AddonDefinitionId);
                
                await _context.AddonInstances.AddAsync(instance);
                await _context.SaveChangesAsync();
                
                _logger.LogInformation("Created addon instance {InstanceId}", instance.Id);
                return instance;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating addon instance");
                throw;
            }
        }

        public async Task DeleteAddonInstanceAsync(Guid instanceId)
        {
            try
            {
                _logger.LogInformation("Deleting addon instance {InstanceId}", instanceId);
                
                var instance = await _context.AddonInstances.FindAsync(instanceId);
                if (instance != null)
                {
                    _context.AddonInstances.Remove(instance);
                    await _context.SaveChangesAsync();
                    _logger.LogInformation("Deleted addon instance {InstanceId}", instanceId);
                }
                else
                {
                    _logger.LogWarning("Addon instance {InstanceId} not found", instanceId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting addon instance {InstanceId}", instanceId);
                throw;
            }
        }

        public async Task<PagedResponseDto<GlobalAddonDto>> GetGlobalAddonsAsync(int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting global addons with pagination.");
                
                var query = _context.AddonDefinitions
                    .Where(ad => ad.IsGloballyAvailable && !ad.IsDeleted)
                    .AsQueryable();

                var totalCount = await query.CountAsync();
                var globalAddons = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(ad => new GlobalAddonDto
                    {
                        Id = ad.Id,
                        Name = ad.Name,
                        Description = ad.Description ?? string.Empty,
                        Version = ad.CurrentVersion != null ? ad.CurrentVersion.VersionNumber : "1.0.0",
                        Author = ad.Creator == null ? "Unknown" : ad.Creator.Email,
                        CreatedAt = ad.CreatedAt,
                        IsApproved = ad.Status == AddonStatus.Approved,
                        Pricing = new AddonPricingDto // Assuming default pricing if not explicitly set
                        {
                            Price = ad.Price,
                            Currency = "USD", // Default currency
                            BillingType = ad.BillingType ?? "One-time"
                        }
                    })
                    .ToListAsync();
 
                _logger.LogInformation("Retrieved {Count} global addons", globalAddons.Count);
                return new PagedResponseDto<GlobalAddonDto>(globalAddons, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving global addons");
                throw new ApplicationException("Error retrieving global addons", ex);
            }
        }

        public async Task<bool> DeleteAddonDefinitionAsync(Guid id, bool force = false)
        {
            try
            {
                _logger.LogInformation("Deleting addon definition {AddonId}, force: {Force}", id, force);
                
                var addonDefinition = await _context.AddonDefinitions.FindAsync(id);
                if (addonDefinition == null)
                {
                    _logger.LogWarning("Addon definition {AddonId} not found", id);
                    return false;
                }

                if (!force)
                {
                    bool isInUse = await _context.AddonInstances
                        .AnyAsync(ai => ai.AddonDefinitionId == id);
                    
                    if (isInUse)
                    {
                        _logger.LogWarning("Cannot delete addon definition {AddonId} - it is in use", id);
                        return false;
                    }
                }

                // Check system configuration for soft delete preference
                bool useSoftDelete = await IsSoftDeleteEnabled();

                if (useSoftDelete)
                {
                    addonDefinition.IsDeleted = true;
                    addonDefinition.DeletedAt = DateTime.UtcNow;
                    _context.AddonDefinitions.Update(addonDefinition);
                    _logger.LogInformation("Soft deleted addon definition {AddonId}", id);
                }
                else
                {
                    _context.AddonDefinitions.Remove(addonDefinition);
                    _logger.LogInformation("Hard deleted addon definition {AddonId}", id);
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting addon definition {AddonId}", id);
                throw new ApplicationException("Error deleting addon definition", ex);
            }
        }

        public async Task<AddonPricingDto> GetAddonPricingAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting pricing for addon {AddonId}", id);
                
                var addon = await _context.AddonDefinitions
                    .FirstOrDefaultAsync(a => a.Id == id && !a.IsDeleted);

                if (addon == null)
                {
                    _logger.LogWarning("Addon {AddonId} not found", id);
                    throw new ArgumentException($"Addon with ID {id} not found");
                }

                var pricing = new AddonPricingDto
                {
                    Price = addon.Price, // Assuming Price property exists on AddonDefinition
                    Currency = "USD",
                    BillingType = addon.BillingType ?? "One-time"
                };

                _logger.LogInformation("Retrieved pricing for addon {AddonId}: {Price} {Currency}", id, pricing.Price, pricing.Currency);
                return pricing;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving addon pricing for {AddonId}", id);
                throw new ApplicationException("Error retrieving addon pricing", ex);
            }
        }

        public async Task<Guid> ProcessAddonPurchaseAsync(Guid addonId, PurchaseRequestDto request)
        {
            try
            {
                _logger.LogInformation("Processing addon purchase for addon {AddonId} by user {UserId}", addonId, request.UserId);
                
                var addon = await _context.AddonDefinitions
                    .FirstOrDefaultAsync(a => a.Id == addonId && !a.IsDeleted);

                if (addon == null)
                {
                    _logger.LogWarning("Addon {AddonId} not found for purchase", addonId);
                    throw new ArgumentException($"Addon with ID {addonId} not found");
                }

                if (addon.Status != AddonStatus.Approved)
                {
                    _logger.LogWarning("Addon {AddonId} is not approved for purchase", addonId);
                    throw new InvalidOperationException($"Addon {addon.Name} is not approved for purchase");
                }

                var purchase = new AddonPurchase
                {
                    Id = Guid.NewGuid(),
                    AddonDefinitionId = addonId,
                    UserId = request.UserId,
                    Amount = addon.Price,
                    PaymentMethod = request.PaymentMethod, // Assuming PaymentMethod is part of PurchaseRequestDto
                    BillingDetails = request.BillingDetails, // Assuming BillingDetails is part of PurchaseRequestDto
                    PurchaseDate = DateTime.UtcNow,
                    Status = PurchaseStatus.Completed
                };

                await _context.AddonPurchases.AddAsync(purchase);
                await _context.SaveChangesAsync();

                _logger.LogInformation("Processed addon purchase {PurchaseId} for addon {AddonId}", purchase.Id, addonId);
                return purchase.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing addon purchase for addon {AddonId}", addonId);
                throw new ApplicationException("Error processing addon purchase", ex);
            }
        }

        public async Task<PagedResponseDto<UserPurchaseDto>> GetUserPurchasesAsync(string userId, int pageNumber = 1, int pageSize = 10)
        {
            try
            {
                _logger.LogInformation("Getting purchases for user {UserId} with pagination.", userId);
                
                var userGuid = Guid.Parse(userId);
                var query = _context.AddonPurchases
                    .Where(p => p.UserId == userGuid)
                    .Include(p => p.AddonDefinition)
                    .AsQueryable();

                var totalCount = await query.CountAsync();
                var purchases = await query
                    .Skip((pageNumber - 1) * pageSize)
                    .Take(pageSize)
                    .Select(p => new UserPurchaseDto
                    {
                        PurchaseId = p.Id.ToString(),
                        AddonName = p.AddonDefinition.Name,
                        PurchaseDate = p.PurchaseDate,
                        Amount = p.Amount
                    })
                    .ToListAsync();
 
                _logger.LogInformation("Retrieved {Count} purchases for user {UserId}", purchases.Count, userId);
                return new PagedResponseDto<UserPurchaseDto>(purchases, totalCount, pageNumber, pageSize); // Corrected instantiation
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting purchases for user {UserId}", userId);
                throw new ApplicationException("Error retrieving user purchases", ex);
            }
        }

        public async Task<AddonDraft> SaveAddonDraftAsync(AddonDraft draft)
        {
            _logger.LogInformation("Saving addon draft {DraftName}", draft.Name);
            draft.CreatedAt = DateTime.UtcNow;
            draft.UpdatedAt = DateTime.UtcNow;

            _context.AddonDrafts.Add(draft);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Saved addon draft {DraftId}", draft.Id);
            return draft;
        }

        public async Task<AddonDraft?> GetAddonDraftAsync(Guid draftId)
        {
            _logger.LogInformation("Getting addon draft {DraftId}", draftId);
            var draft = await _context.AddonDrafts.FindAsync(draftId);
            if (draft == null)
            {
                _logger.LogWarning("Addon draft {DraftId} not found", draftId);
            }
            return draft;
        }

        public async Task<bool> PublishAddonDraftAsync(Guid draftId)
        {
            _logger.LogInformation("Publishing addon draft {DraftId}", draftId);
            var draft = await _context.AddonDrafts.FindAsync(draftId);
            if (draft == null)
            {
                _logger.LogWarning("Addon draft {DraftId} not found for publishing", draftId);
                return false;
            }

            // Placeholder for publishing logic
            // This would typically create a new AddonDefinition/AddonVersion
            _logger.LogInformation("Addon draft {DraftId} published successfully (placeholder)", draftId);
            return true;
        }

        public async Task<AddonDefinition> CreateAddonDefinitionAsync(AddonDefinition addonDefinition, Guid creatorId)
        {
            _logger.LogInformation("Creating addon definition {AddonName} by user {CreatorId}", addonDefinition.Name, creatorId);
            addonDefinition.Id = Guid.NewGuid();
            addonDefinition.CreatedAt = DateTime.UtcNow;
            addonDefinition.UpdatedAt = DateTime.UtcNow;
            addonDefinition.CreatorId = creatorId;
            addonDefinition.Status = AddonStatus.Pending;

            _context.AddonDefinitions.Add(addonDefinition);
            await _context.SaveChangesAsync();
            _logger.LogInformation("Created addon definition {AddonId}", addonDefinition.Id);
            return addonDefinition;
        }

        public async Task<PagedResponseDto<AddonDefinition>> GetPendingAddonDefinitionsAsync(int pageNumber = 1, int pageSize = 10)
        {
            _logger.LogInformation("Getting pending addon definitions with pagination.");
            var query = _context.AddonDefinitions
                .Where(a => a.Status == AddonStatus.Pending && !a.IsDeleted)
                .AsQueryable();

            var totalCount = await query.CountAsync();
            var pendingAddons = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} pending addon definitions", pendingAddons.Count);
            return new PagedResponseDto<AddonDefinition>(pendingAddons, totalCount, pageNumber, pageSize); // Corrected instantiation
        }

        public async Task<AddonDefinition?> UpdateAddonDefinitionAsync(Guid id, AddonDefinition addonDefinition, Guid currentUserId, bool isAdmin)
        {
            _logger.LogInformation("Updating addon definition {AddonId} by user {UserId}", id, currentUserId);
            var existingAddon = await _context.AddonDefinitions.FindAsync(id);
            if (existingAddon == null)
            {
                _logger.LogWarning("Addon definition {AddonId} not found for update", id);
                return null;
            }

            if (existingAddon.CreatorId != currentUserId && !isAdmin)
            {
                _logger.LogWarning("User {UserId} does not have permission to update addon definition {AddonId}", currentUserId, id);
                // Consider throwing a specific exception for permission denied
                return null; 
            }

            existingAddon.Name = addonDefinition.Name;
            existingAddon.Description = addonDefinition.Description;
            existingAddon.Price = addonDefinition.Price;
            existingAddon.BillingType = addonDefinition.BillingType;
            existingAddon.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            _logger.LogInformation("Updated addon definition {AddonId}", id);
            return existingAddon;
        }

        public async Task<PagedResponseDto<AddonDefinition>> GetAddonDefinitionsAsync(AddonStatus? status, Guid? currentUserId, bool isAdmin, int pageNumber = 1, int pageSize = 10)
        {
            _logger.LogInformation("Getting addon definitions with status {Status} for user {UserId} (isAdmin: {IsAdmin}) with pagination.", status, currentUserId, isAdmin);
            var query = _context.AddonDefinitions
                .Where(a => !a.IsDeleted)
                .AsQueryable();

            if (status.HasValue)
            {
                query = query.Where(a => a.Status == status.Value);
            }

            if (!isAdmin && currentUserId.HasValue)
            {
                query = query.Where(a => a.Status == AddonStatus.Approved || a.CreatorId == currentUserId.Value);
            }
            else if (!isAdmin) // Non-admin, no specific user (should ideally not happen if auth is correct)
            {
                 query = query.Where(a => a.Status == AddonStatus.Approved);
            }

            var totalCount = await query.CountAsync();
            var addons = await query
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            _logger.LogInformation("Retrieved {Count} addon definitions", addons.Count);
            return new PagedResponseDto<AddonDefinition>(addons, totalCount, pageNumber, pageSize); // Corrected instantiation
        }

        public async Task<AddonDefinition?> GetAddonDefinitionAsync(Guid id, Guid? currentUserId, bool isAdmin)
        {
            _logger.LogInformation("Getting addon definition {AddonId} for user {UserId} (isAdmin: {IsAdmin})", id, currentUserId, isAdmin);
            var addonDefinition = await _context.AddonDefinitions
                .FirstOrDefaultAsync(a => a.Id == id && !a.IsDeleted);

            if (addonDefinition == null)
            {
                _logger.LogWarning("Addon definition {AddonId} not found", id);
                return null;
            }

            if (addonDefinition.Status != AddonStatus.Approved && !isAdmin && currentUserId.HasValue)
            {
                if (addonDefinition.CreatorId != currentUserId.Value)
                {
                    _logger.LogWarning("User {UserId} does not have permission to view addon definition {AddonId}", currentUserId, id);
                    return null; // Or throw
                }
            }
            else if (addonDefinition.Status != AddonStatus.Approved && !isAdmin) // Not admin, no user context to check ownership
            {
                 _logger.LogWarning("Non-admin user attempted to view non-approved addon definition {AddonId} without ownership context", id);
                 return null; // Or throw
            }


            return addonDefinition;
        }

        public async Task<bool> ApproveAddonAsync(Guid id, Guid currentUserId)
        {
            _logger.LogInformation("Approving addon {AddonId} by user {UserId}", id, currentUserId);
            var addon = await _context.AddonDefinitions.FindAsync(id);
            if (addon == null)
            {
                _logger.LogWarning("Addon {AddonId} not found for approval", id);
                return false;
            }

            addon.Status = AddonStatus.Approved;
            addon.ApprovedAt = DateTime.UtcNow;
            addon.RejectionReason = null;
            addon.UpdatedAt = DateTime.UtcNow;

            var auditLog = new AuditLog
            {
                Id = Guid.NewGuid(),
                Action = AuditAction.ApproveAddon,
                RecordId = addon.Id,
                TableName = "AddonDefinition",
                Timestamp = DateTime.UtcNow,
                UserId = currentUserId
            };
            _context.AuditLogs.Add(auditLog);

            await _context.SaveChangesAsync();
            _logger.LogInformation("Addon {AddonId} approved", id);
            return true;
        }

        public async Task<bool> RejectAddonAsync(Guid id, AddonApprovalDto dto, Guid currentUserId)
        {
            _logger.LogInformation("Rejecting addon {AddonId} by user {UserId}", id, currentUserId);
            var addon = await _context.AddonDefinitions.FindAsync(id);
            if (addon == null)
            {
                _logger.LogWarning("Addon {AddonId} not found for rejection", id);
                return false;
            }

            addon.Status = AddonStatus.Rejected;
            addon.RejectionReason = dto.RejectionReason;
            addon.UpdatedAt = DateTime.UtcNow;

            var auditLog = new AuditLog
            {
                Id = Guid.NewGuid(),
                Action = AuditAction.RejectAddon,
                RecordId = addon.Id,
                TableName = "AddonDefinition",
                Timestamp = DateTime.UtcNow,
                UserId = currentUserId
            };
            _context.AuditLogs.Add(auditLog);

            await _context.SaveChangesAsync();
            _logger.LogInformation("Addon {AddonId} rejected with reason: {Reason}", id, dto.RejectionReason);
            return true;
        }

        public async Task<JsonDocument?> GetAddonInstanceDataAsync(Guid addonInstanceId, Guid tenantId)
        {
            _logger.LogInformation("Getting addon instance data for instance {AddonInstanceId}, tenant {TenantId}", addonInstanceId, tenantId);
            var addonInstance = await _context.AddonInstances
                .FirstOrDefaultAsync(ai => ai.Id == addonInstanceId && ai.TenantId == tenantId);

            if (addonInstance == null)
            {
                _logger.LogWarning("Addon instance {AddonInstanceId} not found for tenant {TenantId}", addonInstanceId, tenantId);
                return null;
            }

            // Assuming ConfigurationJson is stored as a string in the database
            if (string.IsNullOrEmpty(addonInstance.ConfigurationJson))
            {
                return JsonDocument.Parse("{}"); // Return empty JSON if no configuration
            }

            try
            {
                return JsonDocument.Parse(addonInstance.ConfigurationJson);
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Error parsing ConfigurationJson for addon instance {AddonInstanceId}", addonInstanceId);
                return JsonDocument.Parse("{}"); // Return empty JSON on parse error
            }
        }

        public async Task<bool> UpdateAddonInstanceDataAsync(Guid addonInstanceId, JsonElement payload, Guid tenantId)
        {
            _logger.LogInformation("Updating addon instance data for instance {AddonInstanceId}, tenant {TenantId}", addonInstanceId, tenantId);
            var addonInstance = await _context.AddonInstances
                .Include(ai => ai.AddonVersion) // Include AddonVersion to access SchemaDefinition
                .FirstOrDefaultAsync(ai => ai.Id == addonInstanceId && ai.TenantId == tenantId);

            if (addonInstance == null)
            {
                _logger.LogWarning("Addon instance {AddonInstanceId} not found for tenant {TenantId}", addonInstanceId, tenantId);
                return false;
            }

            if (addonInstance.AddonVersion == null || addonInstance.AddonVersion.SchemaDefinition == null)
            {
                _logger.LogWarning("Addon version or schema definition not found for addon instance {AddonInstanceId}", addonInstanceId);
                // If no schema, we might still allow update or throw an error based on policy
                // For now, let's assume no schema means no validation, but we should log it.
            }
            else
            {
                // Validate payload against schema
                if (!ValidateInput(addonInstance.AddonVersion.SchemaDefinition.RootElement, payload))
                {
                    _logger.LogWarning("Invalid payload for addon instance {AddonInstanceId} based on schema validation.", addonInstanceId);
                    return false; // Or throw a validation exception
                }
            }

            addonInstance.ConfigurationJson = payload.ToString(); // Store as string
            addonInstance.UpdatedAt = DateTime.UtcNow;

            try
            {
                await _context.SaveChangesAsync();
                _logger.LogInformation("Addon instance {AddonInstanceId} data updated successfully.", addonInstanceId);
                return true;
            }
            catch (DbUpdateException ex)
            {
                _logger.LogError(ex, "Error updating addon instance {AddonInstanceId} data.", addonInstanceId);
                return false;
            }
        }

        private bool ValidateInput(JsonElement schema, JsonElement payload)
        {
            // This is a placeholder for actual schema validation (e.g., using a JSON schema library like NJsonSchema)
            // For a real application, you would use a robust library to validate 'payload' against 'schema'.
            // Example:
            // var schemaObject = NJsonSchema.JsonSchema.FromJsonAsync(schema.ToString()).Result;
            // var errors = schemaObject.Validate(payload.ToString());
            // return !errors.Any();
            _logger.LogWarning("Schema validation is a placeholder. Implement proper JSON schema validation.");
            return true; // Always return true for now as validation is not implemented
        }

        private async Task<bool> IsSoftDeleteEnabled()
        {
            // This would typically come from a system configuration service or a global setting
            // For now, hardcode to true for demonstration
            return await Task.FromResult(true);
        }
    }
}
