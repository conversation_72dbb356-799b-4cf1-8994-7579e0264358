using Microsoft.Extensions.Logging;

namespace VelocityPlatform.Business.Services;

public class MockEmailService : IEmailService
{
    private readonly ILogger<MockEmailService> _logger;

    public MockEmailService(ILogger<MockEmailService> logger)
    {
        _logger = logger;
    }

    public async Task SendEmailConfirmationAsync(string email, string firstName, string confirmationToken)
    {
        _logger.LogInformation("Mock Email: Email confirmation sent to {Email} for {FirstName}. Token: {Token}", 
            email, firstName, confirmationToken);
        
        // In a real implementation, you would send an actual email here
        await Task.Delay(100); // Simulate email sending delay
    }

    public async Task SendPasswordResetEmailAsync(string email, string firstName, string resetToken)
    {
        _logger.LogInformation("Mock Email: Password reset sent to {Email} for {FirstName}. Token: {Token}", 
            email, firstName, resetToken);
        
        await Task.Delay(100);
    }

    public async Task SendWelcomeEmailAsync(string email, string firstName)
    {
        _logger.LogInformation("Mock Email: Welcome email sent to {Email} for {FirstName}", 
            email, firstName);
        
        await Task.Delay(100);
    }

    public async Task SendAccountLockedEmailAsync(string email, string firstName)
    {
        _logger.LogInformation("Mock Email: Account locked notification sent to {Email} for {FirstName}", 
            email, firstName);
        
        await Task.Delay(100);
    }

    public async Task SendPasswordChangedEmailAsync(string email, string firstName)
    {
        _logger.LogInformation("Mock Email: Password changed notification sent to {Email} for {FirstName}", 
            email, firstName);
        
        await Task.Delay(100);
    }
    public async Task SendEmailVerificationAsync(string email, string verificationToken)
    {
        _logger.LogInformation("Mock Email: Email verification sent to {Email}. Token: {Token}",
            email, verificationToken);

        await Task.Delay(100);
    }

    public async Task SendTestEmailAsync(string toEmail, string subject, string message)
    {
        _logger.LogInformation("Mock Email: Test email sent to {Email} with subject '{Subject}' and message: {Message}",
            toEmail, subject, message);

        await Task.Delay(100);
    }
}