using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using VelocityPlatform.Data;
using VelocityPlatform.Models.Entities;

namespace VelocityPlatform.Data
{
    public static class DatabaseSeeder
    {
        public static void Seed(VelocityPlatformDbContext context)
        {
            // Create default tenant if none exists
            if (!context.Tenants.Any())
            {
                var defaultTenant = new Tenant
                {
                    Name = "Default Tenant",
                    Slug = "default-tenant",
                    Configuration = null,
                    SubscriptionPlan = "free",
                    MaxSites = 5,
                    MaxUsers = 10,
                    IsolationLevel = "None",
                    // DataIsolationPolicy = string.Empty,
                    // IsolationPolicy = string.Empty,
                    IsolationEnforcedDate = null
                };
                context.Tenants.Add(defaultTenant);
                context.SaveChanges();
            }

            // Add test users with ASP.NET Core Identity hashed passwords
            if (!context.Users.Any())
            {
                var defaultTenant = context.Tenants.First();
                var passwordHasher = new PasswordHasher<User>();
                var testUser = new User
                {
                    Email = "<EMAIL>",
                    UserName = "testuser",
                    NormalizedUserName = "TESTUSER",
                    NormalizedEmail = "<EMAIL>",
                    EmailConfirmed = true,
                    SecurityStamp = Guid.NewGuid().ToString(),
                    ConcurrencyStamp = Guid.NewGuid().ToString(),
                    PhoneNumber = null,
                    PhoneNumberConfirmed = false,
                    TwoFactorEnabled = false,
                    LockoutEnd = null,
                    LockoutEnabled = true,
                    AccessFailedCount = 0,
                    TenantId = defaultTenant.Id,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow
                };

                // Hash the password using ASP.NET Core Identity's password hasher
                testUser.PasswordHash = passwordHasher.HashPassword(testUser, "password");

                context.Users.Add(testUser);
                context.SaveChanges();
            }
        }
    }
}