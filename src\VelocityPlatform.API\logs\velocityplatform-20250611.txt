[2025-06-11 00:06:10.578 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:10.589 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:10.590 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:11.035 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:11.041 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUZe+vD/dgi1tITOMpgR0fa+lh238mprhun06gYkXoEjxgJ01/gvzzuIzzMX4tUCJ9jKsykM4nsTNnElMhQv2wSLCSYC7uRl60D623pfcYh/+2YcJ/wLtlG9rDTVugnY4Y5wElx1m9hpiLp9SXk+erBbdk2Bj1SEdMmK3HzmkR6H3Dfwx6HAs15sTSy28e9db0NNjTB7VuoZjMcETIgayK79oZSYStav1wmaqalVa8abRw== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:06:11.152 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.174 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.178 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.201 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.204 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.208 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.210 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.366 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.368 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.370 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:06:13.375 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.377 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.523 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.526 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.527 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:06:13.533 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.534 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.555 +01:00 WRN] Login failed: User not found <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.557 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.559 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:06:13.564 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.566 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.587 +01:00 WRN] Login failed: User not found <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:06:13.588 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:06:13.590 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:08:24.099 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:24.103 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:24.105 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:24.270 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:24.272 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUbKVz7ZM7aMSIOEYj6p371jWLkR+sfHxzJNQmi3BmG4yRA8W+SiKzuhT51+HnS4iG8P0I02RdQIn05/ZJqr57HIlT+8NGlViORFqhxu68Aiznuu8e/DBxlOJ1P2KttqSZJlfnPSGCVGLIr6bx5RUETd8Df3z1ldmv3eGu26wXb5Sm0dNTXJfwji6vbPMpNbfdUmKwYSI44INgkObsqNy7fKaqJdOktL4ib5mB6Ix9S9jQ== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:08:24.374 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.381 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.383 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.405 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.407 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.411 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.413 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.554 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.557 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.558 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:08:26.563 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.565 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.706 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.708 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.710 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:08:26.715 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.716 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.737 +01:00 WRN] Login failed: User not found <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.739 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.741 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:08:26.745 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.747 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.768 +01:00 WRN] Login failed: User not found <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:08:26.770 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:08:26.772 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:10:47.710 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:47.715 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:47.716 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:47.876 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:47.878 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUZN+aJVLBcUYXWWOLN/QagDGkCTAzyghXPZPpIYS7K5LrzWuZkx/xojhbKzeSWLhDQ6lqUPsm4bI2cQp2WiLZhYkI87FAWyM4Rm1Mh2HBUmK4qLXXSLavuBCSnP+DohsvCq2OVCo+XERZHFwAFnoyT/uoCTyZB/7VH8bh7580zxLKmzx1exdQ+1TAIxi6XXjr0HVkvf8vjZm9spb0eZEUSuvuABM3ye9rh3TmeSl7ep+g== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:10:47.988 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:49.994 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:49.996 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.020 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.022 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:50.026 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:50.027 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.168 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.171 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:50.173 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:10:50.177 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:50.179 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.335 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.338 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:10:50.371 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:10:50.443 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:10:50.479 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:10:50.502 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:10:50.516 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:10:50.642 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:10:50.675 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:10:50.678 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:10:50.701 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:10:50.722 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:10:50.750 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:10:50.751 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:10:50.797 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:10:50.808 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:10:50.836 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:10:50.838 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:10:50.862 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:12:19.410 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:19.414 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:19.416 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:19.758 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:19.761 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUbrRTK4IvlMzP5r3n9cDz1gjig97Sfp3DpMj6RwkI6O8HWncte8HUZt5Eekw4CflriA/HcM9Ptx8aKtFLTlPw9gCown00amo60RPkznhxrRZKsC4zTqE0ZF7DzSouTDb+BF527xoNDQmoV3mhFtf2i2c4YX/8HedSiO2sm26COl9LveN/KFWaJhkJXTS27gE+EQjMzpfi8PayGh2IxkZVHplIw7eYlX0HjFaBBXr+Gm3w== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:12:19.864 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:21.872 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:21.874 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:21.896 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:21.898 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:21.902 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:21.903 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:22.043 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:22.047 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:22.048 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:12:22.053 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:22.055 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:22.139 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:22.141 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:12:22.146 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:12:22.174 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:12:22.199 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:12:22.223 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:12:22.231 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:12:22.308 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:12:22.352 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:12:22.360 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:12:22.401 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:12:22.404 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:12:22.445 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:19:49.660 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-11 00:19:51.615 +01:00 INF] Database migration completed successfully <s:>
[2025-06-11 00:19:51.763 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-11 00:19:51.833 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:19:51.835 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:19:51.836 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:19:51.838 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:20:25.524 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:25.621 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:25.625 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:26.086 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:26.090 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUa8yh4EKqC9HT2rC2XD+zAvYTl4IfqiPlkUukQsi5+8n9RFMlSFiN/cND2WYUddBwmKKW/sY3iRiPS1Ft9GXRIVbQJ2QAnaflROvabnkeBJdF83ttnL0bCtjNIYGfGfCSRT3klcVMZEmazzD2F2X+6LRAuvflXQDle03uB7jlifqweNjXYxR9YXXYyH/rtIKG9R+4ihTBZZfB7GJNty0gsGHuJd/czV9QVTHidKKmn1DA== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:20:26.206 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.256 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.261 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.360 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.363 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.368 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.370 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.531 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.535 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.538 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:20:28.544 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.546 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.646 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.649 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:20:28.682 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:20:28.739 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:20:28.775 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:20:28.797 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:20:28.811 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:20:28.938 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:20:28.972 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:20:28.974 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:20:28.998 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:20:29.019 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:20:29.046 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:20:29.048 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:20:29.092 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:20:29.104 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:20:29.132 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:20:29.134 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:20:29.157 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:20:29.358 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:20:29.361 +01:00 WRN] Payments table not available - returning empty payment history <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:20:29.381 +01:00 INF] Getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", page 1 <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:20:29.385 +01:00 ERR] Error getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.SubscriptionService>
System.InvalidOperationException: The expression 's.SubscriptionPlan' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393.
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.PopulateIncludeTree(IncludeTreeNode includeTreeNode, Expression expression, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.ProcessInclude(NavigationExpansionExpression source, Expression expression, Boolean thenInclude, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.Expand(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryTranslationPreprocessor.Process(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.CountAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 283
[2025-06-11 00:20:29.399 +01:00 ERR] An unhandled exception occurred: The expression 's.SubscriptionPlan' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: The expression 's.SubscriptionPlan' is invalid inside an 'Include' operation, since it does not represent a property access: 't => t.MyProperty'. To target navigations declared on derived types, use casting ('t => ((Derived)t).MyProperty') or the 'as' operator ('t => (t as Derived).MyProperty'). Collection navigation access can be filtered by composing Where, OrderBy(Descending), ThenBy(Descending), Skip or Take operations. For more information on including related data, see https://go.microsoft.com/fwlink/?LinkID=746393.
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.PopulateIncludeTree(IncludeTreeNode includeTreeNode, Expression expression, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.ProcessInclude(NavigationExpansionExpression source, Expression expression, Boolean thenInclude, Boolean setLoaded)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.VisitMethodCall(MethodCallExpression methodCallExpression)
   at Microsoft.EntityFrameworkCore.Query.Internal.NavigationExpandingExpressionVisitor.Expand(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryTranslationPreprocessor.Process(Expression query)
   at Microsoft.EntityFrameworkCore.Query.QueryCompilationContext.CreateQueryExecutor[TResult](Expression query)
   at Microsoft.EntityFrameworkCore.Storage.Database.CompileQuery[TResult](Expression query, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.CompileQueryCore[TResult](IDatabase database, Expression query, IModel model, Boolean async)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.<>c__DisplayClass12_0`1.<ExecuteAsync>b__0()
   at Microsoft.EntityFrameworkCore.Query.Internal.CompiledQueryCache.GetOrAddQuery[TResult](Object cacheKey, Func`1 compiler)
   at Microsoft.EntityFrameworkCore.Query.Internal.QueryCompiler.ExecuteAsync[TResult](Expression query, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.EntityQueryProvider.ExecuteAsync[TResult](Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, Expression expression, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ExecuteAsync[TSource,TResult](MethodInfo operatorMethodInfo, IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.CountAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 283
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptions(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 85
   at lambda_method840(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-11 00:20:29.452 +01:00 ERR] Failed executing DbCommand (20ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."BillingCycle", s."CreatedAt", s."CreatedByUserId", s."Currency", s."Description", s."Features", s."IsActive", s."IsUsageBased", s."LastModifiedByUserId", s."Name", s."Price", s."TenantId", s."UpdatedAt"
FROM "SubscriptionPlans" AS s
WHERE s."IsActive" <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-11 00:20:29.463 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column s.CreatedByUserId does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.CreatedByUserId does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column s.CreatedByUserId does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.CreatedByUserId does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-11 00:20:29.476 +01:00 ERR] An unhandled exception occurred: 42703: column s.CreatedByUserId does not exist

POSITION: 49 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column s.CreatedByUserId does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetAvailablePlansAsync() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 26
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptionPlans() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 36
   at lambda_method848(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.CreatedByUserId does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-11 00:22:45.304 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-11 00:22:46.300 +01:00 WRN] The foreign key property 'UserSubscription.SubscriptionPlanId1' was created in shadow state because a conflicting property with the simple name 'SubscriptionPlanId' exists in the entity type, but is either not mapped, is already used for another relationship, or is incompatible with the associated primary key type. See https://aka.ms/efcore-relationships for information on mapping relationships in EF Core. <s:Microsoft.EntityFrameworkCore.Model.Validation>
[2025-06-11 00:22:47.406 +01:00 INF] Database migration completed successfully <s:>
[2025-06-11 00:22:47.586 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-11 00:22:47.670 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:22:47.672 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:22:47.673 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:22:47.674 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:23:16.976 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:17.086 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:17.091 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:17.620 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:17.624 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUbQ074L9RtmsO5RL+sx4TeLd08UO2bFganSFOuZmYTYIbrwGNxlTLOpFe03t1nAq78fBwSQ72ODONDLqa4IX0h96S11tHTTzmATigqQUAvpoTwezYfNyAbqYLmP3dhSoctmKMcDiSWfy1lv+qzqFSJWGUVsRXCon3fTXuX+MgMro/MrznY/W66kh3ir6ZqWc9X7kYnCna6j36dGqYTfciFVCm14xI46SSkeCwiqV/FBpQ== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:23:17.741 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:19.792 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:19.795 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:19.913 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:19.915 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:19.920 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:19.922 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:19.948 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:19.951 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:19.955 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:23:19.962 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:19.964 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:20.079 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:20.081 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:20.119 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:20.186 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:23:20.225 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:23:20.248 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:23:20.265 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:23:20.405 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:23:20.439 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:23:20.441 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:23:20.467 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:23:20.492 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:23:20.522 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:23:20.523 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:23:20.568 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:23:20.580 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:23:20.608 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:23:20.609 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:23:20.634 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:23:20.943 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:23:20.945 +01:00 WRN] Payments table not available - returning empty payment history <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:23:20.988 +01:00 INF] Getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", page 1 <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:23:21.060 +01:00 ERR] Failed executing DbCommand (22ms) [Parameters=[@__tenantId_0='?' (DbType = Guid), @__userId_Value_1='?' (DbType = Guid), @__p_3='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."EndDate", t."IsActive", t."StartDate", t."Status", t."SubscriptionPlanId", t."SubscriptionPlanId1", t."TenantId", t."UpdatedAt", t."UserId", s."Id", s."BillingCycle", s."CreatedAt", s."Currency", s."Description", s."Features", s."IsActive", s."IsUsageBased", s."Name", s."Price", s."TenantId", s."UpdatedAt", t."Id0", t."AccessFailedCount", t."AnonymizedDate", t."ApiKey", t."AvatarUrl", t."ConcurrencyStamp", t."ConfirmationToken", t."ConfirmationTokenExpiry", t."CreatedAt0", t."Email", t."EmailConfirmed", t."EmailVerified", t."FailedLoginAttempts", t."FirstName", t."IsActive0", t."IsAnonymized", t."IsLockedOut", t."LastLoginAt", t."LastName", t."LastPasswordChange", t."LockedUntil", t."LockoutEnabled", t."LockoutEnd", t."NormalizedEmail", t."NormalizedUserName", t."PasswordHash", t."PasswordSalt", t."PhoneNumber", t."PhoneNumberConfirmed", t."PreferencesJson", t."ProfileData", t."ResetToken", t."ResetTokenExpiry", t."Role", t."SecurityStamp", t."TenantId0", t."TwoFactorEnabled", t."UpdatedAt0", t."UserName"
FROM (
    SELECT u."Id", u."CreatedAt", u."EndDate", u."IsActive", u."StartDate", u."Status", u."SubscriptionPlanId", u."SubscriptionPlanId1", u."TenantId", u."UpdatedAt", u."UserId", a."Id" AS "Id0", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt" AS "CreatedAt0", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive" AS "IsActive0", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId" AS "TenantId0", a."TwoFactorEnabled", a."UpdatedAt" AS "UpdatedAt0", a."UserName"
    FROM "UserSubscriptions" AS u
    INNER JOIN "AspNetUsers" AS a ON u."UserId" = a."Id"
    WHERE a."TenantId" = @__tenantId_0 AND u."UserId" = @__userId_Value_1
    ORDER BY u."CreatedAt" DESC
    LIMIT @__p_3 OFFSET @__p_2
) AS t
INNER JOIN "SubscriptionPlans" AS s ON t."SubscriptionPlanId" = s."Id"
ORDER BY t."CreatedAt" DESC <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-11 00:23:21.072 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn
[2025-06-11 00:23:21.087 +01:00 ERR] Error getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.SubscriptionService>
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 284
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn
[2025-06-11 00:23:21.102 +01:00 ERR] An unhandled exception occurred: 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 284
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptions(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 85
   at lambda_method842(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn
[2025-06-11 00:23:21.155 +01:00 ERR] Failed executing DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."BillingCycle", s."CreatedAt", s."Currency", s."Description", s."Features", s."IsActive", s."IsUsageBased", s."Name", s."Price", s."TenantId", s."UpdatedAt"
FROM "SubscriptionPlans" AS s
WHERE s."IsActive" <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-11 00:23:21.160 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column s.Currency does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.Currency does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column s.Currency does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.Currency does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-11 00:23:21.176 +01:00 ERR] An unhandled exception occurred: 42703: column s.Currency does not exist

POSITION: 49 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column s.Currency does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetAvailablePlansAsync() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 26
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptionPlans() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 36
   at lambda_method856(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.Currency does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-11 00:23:27.163 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:27.169 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:27.171 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:27.359 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:27.361 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUZ18dsgKbUqyDY50xIlZGFgmIYIIyulT+zf8HTFoPy+g7eh5jmO1LS3zlV+4ziNTZbtQX858R1J2q3wavErY+Tfl5qIaS4tZQD8WDjTu9Kbfqbr4m8egFlIuZCec7nrNeFOKKvmMJ2pAQCtl6RAeDsrpjrkC0hXGZKfo3da81ZsfFhNZ1IQh24bV9N9ntxjqZCxN4NnW+mow8lu4wGcCuwax2Q5VJmcizaM3lVz8Un02w== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:23:27.468 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:29.474 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:23:29.476 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:29.510 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:23:29.512 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:24:19.876 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:24:19.878 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:24:19.907 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:24:19.909 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:24:19.911 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:24:19.916 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:24:19.917 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:24:20.002 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:24:20.004 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:24:20.009 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:24:20.036 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:24:20.061 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:24:20.085 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:24:20.091 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:24:20.159 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:24:20.206 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:24:20.212 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:24:20.253 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:24:20.262 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:24:20.306 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:24:20.585 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:24:20.588 +01:00 WRN] Payments table not available - returning empty payment history <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:24:20.594 +01:00 INF] Getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", page 1 <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:24:20.634 +01:00 ERR] Failed executing DbCommand (19ms) [Parameters=[@__tenantId_0='?' (DbType = Guid), @__userId_Value_1='?' (DbType = Guid), @__p_3='?' (DbType = Int32), @__p_2='?' (DbType = Int32)], CommandType='"Text"', CommandTimeout='30']
SELECT t."Id", t."CreatedAt", t."EndDate", t."IsActive", t."StartDate", t."Status", t."SubscriptionPlanId", t."SubscriptionPlanId1", t."TenantId", t."UpdatedAt", t."UserId", s."Id", s."BillingCycle", s."CreatedAt", s."Currency", s."Description", s."Features", s."IsActive", s."IsUsageBased", s."Name", s."Price", s."TenantId", s."UpdatedAt", t."Id0", t."AccessFailedCount", t."AnonymizedDate", t."ApiKey", t."AvatarUrl", t."ConcurrencyStamp", t."ConfirmationToken", t."ConfirmationTokenExpiry", t."CreatedAt0", t."Email", t."EmailConfirmed", t."EmailVerified", t."FailedLoginAttempts", t."FirstName", t."IsActive0", t."IsAnonymized", t."IsLockedOut", t."LastLoginAt", t."LastName", t."LastPasswordChange", t."LockedUntil", t."LockoutEnabled", t."LockoutEnd", t."NormalizedEmail", t."NormalizedUserName", t."PasswordHash", t."PasswordSalt", t."PhoneNumber", t."PhoneNumberConfirmed", t."PreferencesJson", t."ProfileData", t."ResetToken", t."ResetTokenExpiry", t."Role", t."SecurityStamp", t."TenantId0", t."TwoFactorEnabled", t."UpdatedAt0", t."UserName"
FROM (
    SELECT u."Id", u."CreatedAt", u."EndDate", u."IsActive", u."StartDate", u."Status", u."SubscriptionPlanId", u."SubscriptionPlanId1", u."TenantId", u."UpdatedAt", u."UserId", a."Id" AS "Id0", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt" AS "CreatedAt0", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive" AS "IsActive0", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId" AS "TenantId0", a."TwoFactorEnabled", a."UpdatedAt" AS "UpdatedAt0", a."UserName"
    FROM "UserSubscriptions" AS u
    INNER JOIN "AspNetUsers" AS a ON u."UserId" = a."Id"
    WHERE a."TenantId" = @__tenantId_0 AND u."UserId" = @__userId_Value_1
    ORDER BY u."CreatedAt" DESC
    LIMIT @__p_3 OFFSET @__p_2
) AS t
INNER JOIN "SubscriptionPlans" AS s ON t."SubscriptionPlanId" = s."Id"
ORDER BY t."CreatedAt" DESC <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-11 00:24:20.639 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn
[2025-06-11 00:24:20.653 +01:00 ERR] Error getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.SubscriptionService>
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 284
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn
[2025-06-11 00:24:20.665 +01:00 ERR] An unhandled exception occurred: 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column u.SubscriptionPlanId1 does not exist

POSITION: 1176
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetUserSubscriptionsAsync(Guid tenantId, Nullable`1 userId, Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 284
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptions(Int32 pageNumber, Int32 pageSize, String sortBy, String sortOrder, String filter, String searchTerm) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 85
   at lambda_method842(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column u.SubscriptionPlanId1 does not exist
    Hint: Perhaps you meant to reference the column "u.SubscriptionPlanId".
    Position: 1176
    File: parse_relation.c
    Line: 3732
    Routine: errorMissingColumn
[2025-06-11 00:24:20.705 +01:00 ERR] Failed executing DbCommand (19ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
SELECT s."Id", s."BillingCycle", s."CreatedAt", s."Currency", s."Description", s."Features", s."IsActive", s."IsUsageBased", s."Name", s."Price", s."TenantId", s."UpdatedAt"
FROM "SubscriptionPlans" AS s
WHERE s."IsActive" <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-11 00:24:20.726 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column s.Currency does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.Currency does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column s.Currency does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.Currency does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-11 00:24:20.739 +01:00 ERR] An unhandled exception occurred: 42703: column s.Currency does not exist

POSITION: 49 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column s.Currency does not exist

POSITION: 49
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.EntityFrameworkQueryableExtensions.ToListAsync[TSource](IQueryable`1 source, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.SubscriptionService.GetAvailablePlansAsync() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\SubscriptionService.cs:line 26
   at VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptionPlans() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\SubscriptionsController.cs:line 36
   at lambda_method856(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 41
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column s.Currency does not exist
    Position: 49
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-11 00:25:44.792 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-11 00:25:46.786 +01:00 INF] Database migration completed successfully <s:>
[2025-06-11 00:25:46.940 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-11 00:25:47.015 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:25:47.018 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:25:47.019 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:25:47.021 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:26:35.577 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:35.673 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:35.676 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:36.138 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:36.143 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUYsLIm4P8UeYhIdF3BOKx1v408d0Ty1gyh5+CDDQJ3hUQ3hRavsIjy7FPFXV95bI3k2j55aYZGncihPYBfUMpyCQiS5hMqx/PaBOltfY+yIuPto3DB0ylUdHIam3vsjeSF/8PMnE0Iyy69dNr5A7dzRZkg0fo6Cd4EuOxQwsu0Ql2B5H89dHb5QTQEcoTjTTsrZhlVtEgYGkZ8E6jxApKol1JULHiyFXxxw8mnhPgIXJQ== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:26:36.250 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.299 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.304 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.404 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.407 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.413 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.415 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.578 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.582 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.585 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:26:38.591 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.593 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.695 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.696 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:26:38.729 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:26:38.785 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:26:38.824 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:26:38.847 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:26:38.861 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:26:38.992 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:26:39.027 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:26:39.029 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:26:39.053 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:26:39.075 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:26:39.104 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:26:39.107 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:26:39.153 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:26:39.166 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:26:39.194 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:26:39.195 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:26:39.218 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:26:39.537 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:26:39.540 +01:00 WRN] Payments table not available - returning empty payment history <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:26:39.575 +01:00 INF] Getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", page 1 <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:26:39.641 +01:00 INF] Retrieved 0 subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:33:06.289 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-11 00:33:08.931 +01:00 INF] Database migration completed successfully <s:>
[2025-06-11 00:33:09.157 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-11 00:33:09.348 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:33:09.353 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:33:09.357 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:33:09.360 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-11 00:33:41.167 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:41.333 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:41.347 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:42.137 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:42.149 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUZZaXdMYZpRkxvsKFmGRQTeXnmuodZEZFTFBQS4yxXk9E7Biq7DZGv0p6xeUJoEuGXhGSci7GaMGQETDiKjX85HlqyJK/kdiCBOYo147IwFfIxA1O59Wm+UsyX0PBiBk7Az3GN6JPS/cTzmiAt/HwKAfcnJjcYSPeuY996yRS79piGiqQioQtvdiNyzPWeyCLSFdelle8nU2Bn1vaLf0zYpgQyN3qtRjCKtcNXUCLSS/g== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-11 00:33:42.263 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:44.384 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:44.396 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:44.567 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:44.573 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:44.584 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:44.597 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:44.831 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:44.845 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:44.856 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-11 00:33:44.871 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:44.881 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:45.034 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:45.041 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-11 00:33:45.104 +01:00 INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-11 00:33:45.194 +01:00 INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:33:45.250 +01:00 INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:33:45.279 +01:00 WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions. <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:33:45.328 +01:00 INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a". <s:VelocityPlatform.Business.Services.UserService>
[2025-06-11 00:33:45.571 +01:00 INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:33:45.611 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:33:45.621 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:33:45.653 +01:00 INF] Retrieved 0 addon definitions <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:33:45.700 +01:00 INF] Getting global addons with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:33:45.743 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:33:45.747 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:33:45.804 +01:00 INF] Retrieved 0 global addons <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:33:45.831 +01:00 INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination. <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:33:45.866 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:33:45.874 +01:00 WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased. <s:Microsoft.EntityFrameworkCore.Query>
[2025-06-11 00:33:45.903 +01:00 INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a <s:VelocityPlatform.Business.Services.AddonService>
[2025-06-11 00:33:46.294 +01:00 INF] Fetching payment history for Tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", Page 1, PageSize 10 <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:33:46.305 +01:00 WRN] Payments table not available - returning empty payment history <s:VelocityPlatform.Business.Services.PaymentService>
[2025-06-11 00:33:46.363 +01:00 INF] Getting subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", page 1 <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:33:46.461 +01:00 INF] Retrieved 0 subscriptions for tenant "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.Business.Services.SubscriptionService>
[2025-06-11 00:33:46.558 +01:00 INF] Getting current tenant information for "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248" <s:VelocityPlatform.API.Controllers.TenantController>
