using AutoMapper;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Enums;

namespace VelocityPlatform.API.Mapping
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            // User mappings
            CreateMap<User, UserProfileDto>()
                .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.UserName ?? string.Empty))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email ?? string.Empty))
                .ForMember(dest => dest.Permissions, opt => opt.Ignore()) // Will be populated separately
                .ForMember(dest => dest.Preferences, opt => opt.Ignore()) // Will be populated separately
                .ForMember(dest => dest.IsAnonymized, opt => opt.MapFrom(src => false)) // Default value
                .ForMember(dest => dest.AnonymizedDate, opt => opt.MapFrom(src => (DateTime?)null)); // Default value

            CreateMap<UserCreateDto, User>()
                .ForMember(dest => dest.Id, opt => opt.Ignore()) // Will be set in service
                .ForMember(dest => dest.UserName, opt => opt.MapFrom(src => src.Email))
                .ForMember(dest => dest.NormalizedUserName, opt => opt.Ignore()) // Handled by Identity
                .ForMember(dest => dest.NormalizedEmail, opt => opt.Ignore()) // Handled by Identity
                .ForMember(dest => dest.EmailConfirmed, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.PasswordHash, opt => opt.Ignore()) // Handled by Identity
                .ForMember(dest => dest.SecurityStamp, opt => opt.Ignore()) // Handled by Identity
                .ForMember(dest => dest.ConcurrencyStamp, opt => opt.Ignore()) // Handled by Identity
                .ForMember(dest => dest.PhoneNumber, opt => opt.Ignore())
                .ForMember(dest => dest.PhoneNumberConfirmed, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.TwoFactorEnabled, opt => opt.MapFrom(src => false))
                .ForMember(dest => dest.LockoutEnd, opt => opt.Ignore())
                .ForMember(dest => dest.LockoutEnabled, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.AccessFailedCount, opt => opt.MapFrom(src => 0))
                .ForMember(dest => dest.ProfileData, opt => opt.Ignore())
                .ForMember(dest => dest.AvatarUrl, opt => opt.Ignore())
                .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => true))
                .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.UpdatedAt, opt => opt.MapFrom(src => DateTime.UtcNow))
                .ForMember(dest => dest.Tenant, opt => opt.Ignore())
                .ForMember(dest => dest.OwnedSites, opt => opt.Ignore());

            // Site mappings
            CreateMap<Site, SiteDto>()
                .ForMember(dest => dest.OwnerName, opt => opt.MapFrom(src =>
                    src.Owner != null ? $"{src.Owner.FirstName} {src.Owner.LastName}".Trim() : string.Empty));

            // Tenant mappings
            CreateMap<Tenant, TenantDto>();
            CreateMap<Tenant, AdminTenantDto>();

            // Admin mappings
            CreateMap<User, AdminUserDto>();

            // Addon mappings
            CreateMap<AddonDefinition, AddonDefinitionDto>();

            // Subscription mappings
            CreateMap<UserSubscription, UserSubscriptionDto>();
        }
    }
}
