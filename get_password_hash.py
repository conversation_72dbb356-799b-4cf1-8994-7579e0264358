#!/usr/bin/env python3
"""
Script to get password hash from existing users
"""

import psycopg2

def get_password_hashes():
    """Get password hashes from existing users"""
    
    connection_string = {
        'host': '**************',
        'port': 5432,
        'database': 'VWPLATFORMWEB',
        'user': 'PLATFORMDB',
        'password': '$Jf6sSkfyPb&v7r1'
    }
    
    try:
        # Connect to database
        conn = psycopg2.connect(**connection_string)
        cursor = conn.cursor()
        
        print(f"Connected to database successfully")
        
        # Get all users with their password hashes
        cursor.execute('SELECT "Email", "PasswordHash", "EmailConfirmed" FROM "AspNetUsers" LIMIT 10')
        users = cursor.fetchall()
        
        print(f"\nFound {len(users)} users:")
        for email, password_hash, email_confirmed in users:
            print(f"Email: {email}")
            print(f"Confirmed: {email_confirmed}")
            print(f"Hash: {password_hash[:50]}...")
            print("-" * 50)
        
        return users
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return []
        
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    get_password_hashes()
