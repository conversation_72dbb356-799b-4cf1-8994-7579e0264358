2025-06-11 00:06:10.578 +01:00 [INF] Test endpoint hit.
2025-06-11 00:06:10.589 +01:00 [INF] Registration <NAME_EMAIL>
2025-06-11 00:06:10.590 +01:00 [INF] Registration attempt for email: <EMAIL>
2025-06-11 00:06:11.035 +01:00 [INF] User <EMAIL> registered successfully. Sending confirmation email.
2025-06-11 00:06:11.041 +01:00 [INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUZe+vD/dgi1tITOMpgR0fa+lh238mprhun06gYkXoEjxgJ01/gvzzuIzzMX4tUCJ9jKsykM4nsTNnElMhQv2wSLCSYC7uRl60D623pfcYh/+2YcJ/wLtlG9rDTVugnY4Y5wElx1m9hpiLp9SXk+erBbdk2Bj1SEdMmK3HzmkR6H3Dfwx6HAs15sTSy28e9db0NNjTB7VuoZjMcETIgayK79oZSYStav1wmaqalVa8abRw==
2025-06-11 00:06:11.152 +01:00 [INF] Registration <NAME_EMAIL>. Please check email for confirmation.
2025-06-11 00:06:13.174 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:06:13.178 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:06:13.201 +01:00 [WRN] Login failed: Email not confirmed <NAME_EMAIL>
2025-06-11 00:06:13.204 +01:00 [WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox.
2025-06-11 00:06:13.208 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:06:13.210 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:06:13.366 +01:00 [WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false
2025-06-11 00:06:13.368 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:06:13.370 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:06:13.375 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:06:13.377 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:06:13.523 +01:00 [WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false
2025-06-11 00:06:13.526 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:06:13.527 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:06:13.533 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:06:13.534 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:06:13.555 +01:00 [WRN] Login failed: User not found <NAME_EMAIL>
2025-06-11 00:06:13.557 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:06:13.559 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:06:13.564 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:06:13.566 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:06:13.587 +01:00 [WRN] Login failed: User not found <NAME_EMAIL>
2025-06-11 00:06:13.588 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:06:13.590 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:08:24.099 +01:00 [INF] Test endpoint hit.
2025-06-11 00:08:24.103 +01:00 [INF] Registration <NAME_EMAIL>
2025-06-11 00:08:24.105 +01:00 [INF] Registration attempt for email: <EMAIL>
2025-06-11 00:08:24.270 +01:00 [INF] User <EMAIL> registered successfully. Sending confirmation email.
2025-06-11 00:08:24.272 +01:00 [INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUbKVz7ZM7aMSIOEYj6p371jWLkR+sfHxzJNQmi3BmG4yRA8W+SiKzuhT51+HnS4iG8P0I02RdQIn05/ZJqr57HIlT+8NGlViORFqhxu68Aiznuu8e/DBxlOJ1P2KttqSZJlfnPSGCVGLIr6bx5RUETd8Df3z1ldmv3eGu26wXb5Sm0dNTXJfwji6vbPMpNbfdUmKwYSI44INgkObsqNy7fKaqJdOktL4ib5mB6Ix9S9jQ==
2025-06-11 00:08:24.374 +01:00 [INF] Registration <NAME_EMAIL>. Please check email for confirmation.
2025-06-11 00:08:26.381 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:08:26.383 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:08:26.405 +01:00 [WRN] Login failed: Email not confirmed <NAME_EMAIL>
2025-06-11 00:08:26.407 +01:00 [WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox.
2025-06-11 00:08:26.411 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:08:26.413 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:08:26.554 +01:00 [WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false
2025-06-11 00:08:26.557 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:08:26.558 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:08:26.563 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:08:26.565 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:08:26.706 +01:00 [WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false
2025-06-11 00:08:26.708 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:08:26.710 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:08:26.715 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:08:26.716 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:08:26.737 +01:00 [WRN] Login failed: User not found <NAME_EMAIL>
2025-06-11 00:08:26.739 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:08:26.741 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:08:26.745 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:08:26.747 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:08:26.768 +01:00 [WRN] Login failed: User not found <NAME_EMAIL>
2025-06-11 00:08:26.770 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:08:26.772 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:10:47.710 +01:00 [INF] Test endpoint hit.
2025-06-11 00:10:47.715 +01:00 [INF] Registration <NAME_EMAIL>
2025-06-11 00:10:47.716 +01:00 [INF] Registration attempt for email: <EMAIL>
2025-06-11 00:10:47.876 +01:00 [INF] User <EMAIL> registered successfully. Sending confirmation email.
2025-06-11 00:10:47.878 +01:00 [INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUZN+aJVLBcUYXWWOLN/QagDGkCTAzyghXPZPpIYS7K5LrzWuZkx/xojhbKzeSWLhDQ6lqUPsm4bI2cQp2WiLZhYkI87FAWyM4Rm1Mh2HBUmK4qLXXSLavuBCSnP+DohsvCq2OVCo+XERZHFwAFnoyT/uoCTyZB/7VH8bh7580zxLKmzx1exdQ+1TAIxi6XXjr0HVkvf8vjZm9spb0eZEUSuvuABM3ye9rh3TmeSl7ep+g==
2025-06-11 00:10:47.988 +01:00 [INF] Registration <NAME_EMAIL>. Please check email for confirmation.
2025-06-11 00:10:49.994 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:10:49.996 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:10:50.020 +01:00 [WRN] Login failed: Email not confirmed <NAME_EMAIL>
2025-06-11 00:10:50.022 +01:00 [WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox.
2025-06-11 00:10:50.026 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:10:50.027 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:10:50.168 +01:00 [WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false
2025-06-11 00:10:50.171 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:10:50.173 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:10:50.177 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:10:50.179 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:10:50.335 +01:00 [INF] Login successful <NAME_EMAIL>
2025-06-11 00:10:50.338 +01:00 [INF] Login <NAME_EMAIL>
2025-06-11 00:10:50.371 +01:00 [INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a"
2025-06-11 00:10:50.443 +01:00 [INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a"
2025-06-11 00:10:50.479 +01:00 [INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false
2025-06-11 00:10:50.502 +01:00 [WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions.
2025-06-11 00:10:50.516 +01:00 [INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a".
2025-06-11 00:10:50.642 +01:00 [INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination.
2025-06-11 00:10:50.675 +01:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-11 00:10:50.678 +01:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-11 00:10:50.701 +01:00 [INF] Retrieved 0 addon definitions
2025-06-11 00:10:50.722 +01:00 [INF] Getting global addons with pagination.
2025-06-11 00:10:50.750 +01:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-11 00:10:50.751 +01:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-11 00:10:50.797 +01:00 [INF] Retrieved 0 global addons
2025-06-11 00:10:50.808 +01:00 [INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination.
2025-06-11 00:10:50.836 +01:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-11 00:10:50.838 +01:00 [WRN] The query uses a row limiting operator ('Skip'/'Take') without an 'OrderBy' operator. This may lead to unpredictable results. If the 'Distinct' operator is used after 'OrderBy', then make sure to use the 'OrderBy' operator after 'Distinct' as the ordering would otherwise get erased.
2025-06-11 00:10:50.862 +01:00 [INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a
2025-06-11 00:12:19.410 +01:00 [INF] Test endpoint hit.
2025-06-11 00:12:19.414 +01:00 [INF] Registration <NAME_EMAIL>
2025-06-11 00:12:19.416 +01:00 [INF] Registration attempt for email: <EMAIL>
2025-06-11 00:12:19.758 +01:00 [INF] User <EMAIL> registered successfully. Sending confirmation email.
2025-06-11 00:12:19.761 +01:00 [INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUbrRTK4IvlMzP5r3n9cDz1gjig97Sfp3DpMj6RwkI6O8HWncte8HUZt5Eekw4CflriA/HcM9Ptx8aKtFLTlPw9gCown00amo60RPkznhxrRZKsC4zTqE0ZF7DzSouTDb+BF527xoNDQmoV3mhFtf2i2c4YX/8HedSiO2sm26COl9LveN/KFWaJhkJXTS27gE+EQjMzpfi8PayGh2IxkZVHplIw7eYlX0HjFaBBXr+Gm3w==
2025-06-11 00:12:19.864 +01:00 [INF] Registration <NAME_EMAIL>. Please check email for confirmation.
2025-06-11 00:12:21.872 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:12:21.874 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:12:21.896 +01:00 [WRN] Login failed: Email not confirmed <NAME_EMAIL>
2025-06-11 00:12:21.898 +01:00 [WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox.
2025-06-11 00:12:21.902 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:12:21.903 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:12:22.043 +01:00 [WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false
2025-06-11 00:12:22.047 +01:00 [WRN] Login <NAME_EMAIL>: Invalid email or password.
2025-06-11 00:12:22.048 +01:00 [WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401.
2025-06-11 00:12:22.053 +01:00 [INF] Login <NAME_EMAIL> from IP ::1
2025-06-11 00:12:22.055 +01:00 [INF] Login attempt for email: <EMAIL>
2025-06-11 00:12:22.139 +01:00 [INF] Login successful <NAME_EMAIL>
2025-06-11 00:12:22.141 +01:00 [INF] Login <NAME_EMAIL>
2025-06-11 00:12:22.146 +01:00 [INF] Fetching user by ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a"
2025-06-11 00:12:22.174 +01:00 [INF] Fetching current user profile for ID: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a"
2025-06-11 00:12:22.199 +01:00 [INF] Fetching users. TenantId: null, Page: 1, PageSize: 10, CurrentUserId: "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", IsPlatformAdmin: false
2025-06-11 00:12:22.223 +01:00 [WRN] User "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" attempted to access users for tenant null without sufficient permissions.
2025-06-11 00:12:22.231 +01:00 [INF] Fetching preferences for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" by "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a".
2025-06-11 00:12:22.308 +01:00 [INF] Getting addon definitions with status null for user "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a" (isAdmin: false) with pagination.
2025-06-11 00:12:22.352 +01:00 [INF] Retrieved 0 addon definitions
2025-06-11 00:12:22.360 +01:00 [INF] Getting global addons with pagination.
2025-06-11 00:12:22.401 +01:00 [INF] Retrieved 0 global addons
2025-06-11 00:12:22.404 +01:00 [INF] Getting purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a with pagination.
2025-06-11 00:12:22.445 +01:00 [INF] Retrieved 0 purchases for user e0d5169b-b25a-4cfb-a4c1-95c764e7f69a
