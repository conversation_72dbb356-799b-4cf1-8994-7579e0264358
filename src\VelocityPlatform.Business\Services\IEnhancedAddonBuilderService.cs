using VelocityPlatform.Models.DTOs;
using System.Text.Json;

namespace VelocityPlatform.Business.Services
{
    /// <summary>
    /// Enhanced service for addon builder functionality
    /// </summary>
    public interface IEnhancedAddonBuilderService
    {
        /// <summary>
        /// Test an addon with sample data
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="testRequest">Test parameters</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Test result</returns>
        Task<AddonTestResultDto?> TestAddonAsync(Guid draftId, AddonTestRequestDto testRequest, Guid tenantId, Guid userId);

        /// <summary>
        /// Validate addon configuration
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Validation result</returns>
        Task<AddonValidationResultDto?> ValidateAddonAsync(Guid draftId, Guid tenantId, Guid userId);

        /// <summary>
        /// Generate API endpoints for an addon
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="request">Endpoint generation request</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Generation result</returns>
        Task<AddonEndpointGenerationResultDto?> GenerateAddonEndpointsAsync(Guid draftId, EndpointGenerationRequestDto request, Guid tenantId, Guid userId);

        /// <summary>
        /// Get Rete.js configuration for an addon
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Rete configuration</returns>
        Task<ReteConfigurationDto?> GetReteConfigurationAsync(Guid draftId, Guid tenantId, Guid userId);

        /// <summary>
        /// Update Rete.js configuration for an addon
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="updateDto">Update data</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Updated configuration</returns>
        Task<ReteConfigurationDto?> UpdateReteConfigurationAsync(Guid draftId, UpdateReteConfigurationDto updateDto, Guid tenantId, Guid userId);

        /// <summary>
        /// Get marketplace categories
        /// </summary>
        /// <returns>Marketplace categories</returns>
        Task<IEnumerable<AddonCategoryDto>> GetMarketplaceCategoriesAsync();

        /// <summary>
        /// Submit addon to marketplace
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="submission">Submission data</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Submission result</returns>
        Task<MarketplaceSubmissionResultDto?> SubmitToMarketplaceAsync(Guid draftId, MarketplaceSubmissionDto submission, Guid tenantId, Guid userId);

        /// <summary>
        /// Get addon performance metrics
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Performance metrics</returns>
        Task<AddonPerformanceMetricsDto?> GetAddonPerformanceMetricsAsync(Guid draftId, Guid tenantId);

        /// <summary>
        /// Generate addon documentation
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Generated documentation</returns>
        Task<AddonDocumentationDto?> GenerateAddonDocumentationAsync(Guid draftId, Guid tenantId, Guid userId);

        /// <summary>
        /// Get addon dependencies
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Addon dependencies</returns>
        Task<IEnumerable<AddonDependencyDto>> GetAddonDependenciesAsync(Guid draftId, Guid tenantId);

        /// <summary>
        /// Validate addon dependencies
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Dependency validation result</returns>
        Task<DependencyValidationResultDto> ValidateAddonDependenciesAsync(Guid draftId, Guid tenantId);

        /// <summary>
        /// Get addon version history
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Version history</returns>
        Task<IEnumerable<AddonVersionDto>> GetAddonVersionHistoryAsync(Guid draftId, Guid tenantId);

        /// <summary>
        /// Create addon version
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="versionData">Version data</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <param name="userId">User ID</param>
        /// <returns>Created version</returns>
        Task<AddonVersionDto?> CreateAddonVersionAsync(Guid draftId, CreateAddonVersionDto versionData, Guid tenantId, Guid userId);

        /// <summary>
        /// Get addon usage analytics
        /// </summary>
        /// <param name="draftId">Addon draft ID</param>
        /// <param name="tenantId">Tenant ID</param>
        /// <returns>Usage analytics</returns>
        Task<AddonUsageAnalyticsDto?> GetAddonUsageAnalyticsAsync(Guid draftId, Guid tenantId);
    }

    /// <summary>
    /// DTO for addon performance metrics
    /// </summary>
    public class AddonPerformanceMetricsDto
    {
        public Guid AddonId { get; set; }
        public int LoadTimeMs { get; set; }
        public int MemoryUsageMB { get; set; }
        public int CpuUsagePercent { get; set; }
        public int BundleSizeBytes { get; set; }
        public int ApiCallCount { get; set; }
        public int ErrorCount { get; set; }
        public decimal SuccessRate { get; set; }
        public DateTime LastMeasured { get; set; }
        public List<string> PerformanceIssues { get; set; } = new();
        public List<string> OptimizationSuggestions { get; set; } = new();
    }

    /// <summary>
    /// DTO for addon documentation
    /// </summary>
    public class AddonDocumentationDto
    {
        public Guid AddonId { get; set; }
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string MarkdownContent { get; set; } = string.Empty;
        public string HtmlContent { get; set; } = string.Empty;
        public List<AddonApiDocumentationDto> ApiDocumentation { get; set; } = new();
        public List<AddonConfigurationDocDto> ConfigurationDocumentation { get; set; } = new();
        public List<AddonExampleDto> Examples { get; set; } = new();
        public DateTime GeneratedAt { get; set; }
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO for addon API documentation
    /// </summary>
    public class AddonApiDocumentationDto
    {
        public string Endpoint { get; set; } = string.Empty;
        public string Method { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public List<ApiParameterDocDto> Parameters { get; set; } = new();
        public List<ApiResponseDocDto> Responses { get; set; } = new();
        public List<string> Examples { get; set; } = new();
    }

    /// <summary>
    /// DTO for API parameter documentation
    /// </summary>
    public class ApiParameterDocDto
    {
        public string Name { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool Required { get; set; }
        public string? DefaultValue { get; set; }
        public string? Example { get; set; }
    }

    /// <summary>
    /// DTO for API response documentation
    /// </summary>
    public class ApiResponseDocDto
    {
        public int StatusCode { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Schema { get; set; }
        public string? Example { get; set; }
    }

    /// <summary>
    /// DTO for addon configuration documentation
    /// </summary>
    public class AddonConfigurationDocDto
    {
        public string PropertyName { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool Required { get; set; }
        public string? DefaultValue { get; set; }
        public List<string>? AllowedValues { get; set; }
        public string? Example { get; set; }
    }

    /// <summary>
    /// DTO for addon example
    /// </summary>
    public class AddonExampleDto
    {
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Language { get; set; } = "javascript";
        public string? Output { get; set; }
    }

    /// <summary>
    /// DTO for addon dependency
    /// </summary>
    public class AddonDependencyDto
    {
        public string Name { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // "npm", "cdn", "internal"
        public bool Required { get; set; }
        public string? Description { get; set; }
        public string? Url { get; set; }
    }

    /// <summary>
    /// DTO for dependency validation result
    /// </summary>
    public class DependencyValidationResultDto
    {
        public bool IsValid { get; set; }
        public List<string> MissingDependencies { get; set; } = new();
        public List<string> VersionConflicts { get; set; } = new();
        public List<string> SecurityIssues { get; set; } = new();
        public List<string> Warnings { get; set; } = new();
    }

    /// <summary>
    /// DTO for addon version
    /// </summary>
    public class AddonVersionDto
    {
        public Guid Id { get; set; }
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public Guid CreatedBy { get; set; }
        public string CreatedByName { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public bool IsPublished { get; set; }
        public string? ChangeLog { get; set; }
    }

    /// <summary>
    /// DTO for creating addon version
    /// </summary>
    public class CreateAddonVersionDto
    {
        public string Version { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string? ChangeLog { get; set; }
        public bool SetAsActive { get; set; } = false;
    }

    /// <summary>
    /// DTO for addon usage analytics
    /// </summary>
    public class AddonUsageAnalyticsDto
    {
        public Guid AddonId { get; set; }
        public int TotalInstalls { get; set; }
        public int ActiveInstalls { get; set; }
        public int TotalUsage { get; set; }
        public int UniqueUsers { get; set; }
        public decimal AverageRating { get; set; }
        public int RatingCount { get; set; }
        public List<UsageByPeriodDto> UsageByPeriod { get; set; } = new();
        public List<UsageByRegionDto> UsageByRegion { get; set; } = new();
        public DateTime LastUpdated { get; set; }
    }

    /// <summary>
    /// DTO for usage by time period
    /// </summary>
    public class UsageByPeriodDto
    {
        public DateTime Period { get; set; }
        public int UsageCount { get; set; }
        public int UniqueUsers { get; set; }
        public int NewInstalls { get; set; }
    }

    /// <summary>
    /// DTO for usage by region
    /// </summary>
    public class UsageByRegionDto
    {
        public string Region { get; set; } = string.Empty;
        public int UsageCount { get; set; }
        public int UniqueUsers { get; set; }
        public double PercentageOfTotal { get; set; }
    }
}
