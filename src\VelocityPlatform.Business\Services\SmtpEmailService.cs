using System.Net;
using System.Net.Mail;
using System.Text;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using VelocityPlatform.Models.Configuration;

namespace VelocityPlatform.Business.Services
{
    public class SmtpEmailService : IEmailService
    {
        private readonly EmailSettings _emailSettings;
        private readonly ILogger<SmtpEmailService> _logger;

        public SmtpEmailService(IOptions<EmailSettings> emailSettings, ILogger<SmtpEmailService> logger)
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
        }

        public async Task SendEmailConfirmationAsync(string email, string firstName, string confirmationToken)
        {
            var subject = "Confirm Your Email Address - VelocityPlatform";
            var confirmationUrl = $"{_emailSettings.BaseUrl}/api/v1/Auth/confirm-email?email={Uri.EscapeDataString(email)}&token={Uri.EscapeDataString(confirmationToken)}";
            
            var body = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Email Confirmation</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>Welcome to VelocityPlatform!</h1>
        </div>
        
        <div style='background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>
            <h2 style='color: #495057; margin-top: 0;'>Hi {firstName},</h2>
            
            <p style='font-size: 16px; margin-bottom: 25px;'>
                Thank you for registering with VelocityPlatform! To complete your registration and start building amazing websites, 
                please confirm your email address by clicking the button below.
            </p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='{confirmationUrl}' 
                   style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                          color: white; 
                          padding: 15px 30px; 
                          text-decoration: none; 
                          border-radius: 5px; 
                          font-weight: bold; 
                          font-size: 16px; 
                          display: inline-block;'>
                    Confirm Email Address
                </a>
            </div>
            
            <p style='font-size: 14px; color: #6c757d; margin-top: 30px;'>
                If the button doesn't work, you can copy and paste this link into your browser:<br>
                <a href='{confirmationUrl}' style='color: #667eea; word-break: break-all;'>{confirmationUrl}</a>
            </p>
            
            <p style='font-size: 14px; color: #6c757d; margin-top: 20px;'>
                This link will expire in 24 hours for security reasons.
            </p>
            
            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>
            
            <p style='font-size: 12px; color: #6c757d; text-align: center; margin: 0;'>
                If you didn't create an account with VelocityPlatform, please ignore this email.
            </p>
        </div>
    </div>
</body>
</html>";

            await SendEmailAsync(email, subject, body);
            _logger.LogInformation("Email confirmation sent to {Email} for {FirstName}", email, firstName);
        }

        public async Task SendPasswordResetEmailAsync(string email, string firstName, string resetToken)
        {
            var subject = "Reset Your Password - VelocityPlatform";
            var resetUrl = $"{_emailSettings.BaseUrl}/reset-password?email={Uri.EscapeDataString(email)}&token={Uri.EscapeDataString(resetToken)}";
            
            var body = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Password Reset</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>Password Reset Request</h1>
        </div>
        
        <div style='background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>
            <h2 style='color: #495057; margin-top: 0;'>Hi {firstName},</h2>
            
            <p style='font-size: 16px; margin-bottom: 25px;'>
                We received a request to reset your password for your VelocityPlatform account. 
                Click the button below to create a new password.
            </p>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='{resetUrl}' 
                   style='background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); 
                          color: white; 
                          padding: 15px 30px; 
                          text-decoration: none; 
                          border-radius: 5px; 
                          font-weight: bold; 
                          font-size: 16px; 
                          display: inline-block;'>
                    Reset Password
                </a>
            </div>
            
            <p style='font-size: 14px; color: #6c757d; margin-top: 30px;'>
                If the button doesn't work, you can copy and paste this link into your browser:<br>
                <a href='{resetUrl}' style='color: #ff6b6b; word-break: break-all;'>{resetUrl}</a>
            </p>
            
            <p style='font-size: 14px; color: #6c757d; margin-top: 20px;'>
                This link will expire in 1 hour for security reasons.
            </p>
            
            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>
            
            <p style='font-size: 12px; color: #6c757d; text-align: center; margin: 0;'>
                If you didn't request a password reset, please ignore this email. Your password will remain unchanged.
            </p>
        </div>
    </div>
</body>
</html>";

            await SendEmailAsync(email, subject, body);
            _logger.LogInformation("Password reset email sent to {Email} for {FirstName}", email, firstName);
        }

        public async Task SendWelcomeEmailAsync(string email, string firstName)
        {
            var subject = "Welcome to VelocityPlatform!";
            
            var body = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Welcome to VelocityPlatform</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #10ac84 0%, #1dd1a1 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>Welcome to VelocityPlatform!</h1>
        </div>
        
        <div style='background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>
            <h2 style='color: #495057; margin-top: 0;'>Hi {firstName},</h2>
            
            <p style='font-size: 16px; margin-bottom: 25px;'>
                Welcome to VelocityPlatform! Your email has been confirmed and your account is now active. 
                You can start building amazing websites with our powerful tools and components.
            </p>
            
            <div style='background: white; padding: 20px; border-radius: 5px; border-left: 4px solid #10ac84; margin: 25px 0;'>
                <h3 style='margin-top: 0; color: #495057;'>What's Next?</h3>
                <ul style='margin: 0; padding-left: 20px;'>
                    <li>Create your first website</li>
                    <li>Explore our component library</li>
                    <li>Check out the addon marketplace</li>
                    <li>Customize your profile settings</li>
                </ul>
            </div>
            
            <div style='text-align: center; margin: 30px 0;'>
                <a href='{_emailSettings.BaseUrl}' 
                   style='background: linear-gradient(135deg, #10ac84 0%, #1dd1a1 100%); 
                          color: white; 
                          padding: 15px 30px; 
                          text-decoration: none; 
                          border-radius: 5px; 
                          font-weight: bold; 
                          font-size: 16px; 
                          display: inline-block;'>
                    Get Started
                </a>
            </div>
            
            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>
            
            <p style='font-size: 12px; color: #6c757d; text-align: center; margin: 0;'>
                Need help? Contact our support <NAME_EMAIL>
            </p>
        </div>
    </div>
</body>
</html>";

            await SendEmailAsync(email, subject, body);
            _logger.LogInformation("Welcome email sent to {Email} for {FirstName}", email, firstName);
        }

        public async Task SendAccountLockedEmailAsync(string email, string firstName)
        {
            var subject = "Account Security Alert - VelocityPlatform";
            
            var body = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Account Locked</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>Account Security Alert</h1>
        </div>
        
        <div style='background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>
            <h2 style='color: #495057; margin-top: 0;'>Hi {firstName},</h2>
            
            <p style='font-size: 16px; margin-bottom: 25px;'>
                Your VelocityPlatform account has been temporarily locked due to multiple failed login attempts. 
                This is a security measure to protect your account.
            </p>
            
            <div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffa502; margin: 25px 0;'>
                <p style='margin: 0; font-weight: bold; color: #856404;'>
                    Your account will be automatically unlocked in 15 minutes.
                </p>
            </div>
            
            <p style='font-size: 14px; color: #6c757d;'>
                If you didn't attempt to log in, please contact our support team immediately.
            </p>
            
            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>
            
            <p style='font-size: 12px; color: #6c757d; text-align: center; margin: 0;'>
                For security concerns, contact <EMAIL>
            </p>
        </div>
    </div>
</body>
</html>";

            await SendEmailAsync(email, subject, body);
            _logger.LogInformation("Account locked notification sent to {Email} for {FirstName}", email, firstName);
        }

        public async Task SendPasswordChangedEmailAsync(string email, string firstName)
        {
            var subject = "Password Changed Successfully - VelocityPlatform";
            
            var body = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Password Changed</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #10ac84 0%, #1dd1a1 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>Password Changed</h1>
        </div>
        
        <div style='background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;'>
            <h2 style='color: #495057; margin-top: 0;'>Hi {firstName},</h2>
            
            <p style='font-size: 16px; margin-bottom: 25px;'>
                Your VelocityPlatform account password has been successfully changed.
            </p>
            
            <div style='background: #d1ecf1; padding: 15px; border-radius: 5px; border-left: 4px solid #10ac84; margin: 25px 0;'>
                <p style='margin: 0; font-weight: bold; color: #0c5460;'>
                    ✓ Password changed on {DateTime.UtcNow:yyyy-MM-dd HH:mm} UTC
                </p>
            </div>
            
            <p style='font-size: 14px; color: #6c757d;'>
                If you didn't make this change, please contact our support team immediately.
            </p>
            
            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>
            
            <p style='font-size: 12px; color: #6c757d; text-align: center; margin: 0;'>
                For security concerns, contact <EMAIL>
            </p>
        </div>
    </div>
</body>
</html>";

            await SendEmailAsync(email, subject, body);
            _logger.LogInformation("Password changed notification sent to {Email} for {FirstName}", email, firstName);
        }

        public async Task SendEmailVerificationAsync(string email, string verificationToken)
        {
            // This is similar to SendEmailConfirmationAsync but for re-verification
            await SendEmailConfirmationAsync(email, "User", verificationToken);
        }

        public async Task SendTestEmailAsync(string toEmail, string subject, string message)
        {
            var body = GenerateTestEmailTemplate(toEmail, subject, message);

            await SendEmailAsync(toEmail, subject, body);
            _logger.LogInformation("Test email sent to {Email} with subject '{Subject}'", toEmail, subject);
        }

        public async Task SendCustomEmailAsync(string toEmail, string subject, string message, bool isHtml = false, string? fromName = null)
        {
            string body;

            if (isHtml)
            {
                // If HTML is provided, use it directly but wrap in basic structure if needed
                body = message.Contains("<html>") ? message : GenerateCustomHtmlTemplate(subject, message, fromName);
            }
            else
            {
                // Convert plain text to HTML with professional styling
                body = GenerateCustomHtmlTemplate(subject, message, fromName);
            }

            await SendEmailAsync(toEmail, subject, body, fromName);
            _logger.LogInformation("Custom email sent to {Email} with subject '{Subject}' (HTML: {IsHtml})", toEmail, subject, isHtml);
        }

        private string GenerateTestEmailTemplate(string toEmail, string subject, string message)
        {
            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>Test Email</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>🧪 Test Email</h1>
            <p style='color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;'>VelocityPlatform SMTP Test</p>
        </div>

        <div style='background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
            <h2 style='color: #333; margin-top: 0; font-size: 24px;'>Test Email Delivery</h2>

            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;'>
                <p style='margin: 0; font-weight: bold; color: #667eea;'>📧 Recipient:</p>
                <p style='margin: 5px 0 0 0; font-family: monospace; background: white; padding: 8px; border-radius: 4px;'>{toEmail}</p>
            </div>

            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;'>
                <p style='margin: 0; font-weight: bold; color: #28a745;'>📝 Subject:</p>
                <p style='margin: 5px 0 0 0; font-family: monospace; background: white; padding: 8px; border-radius: 4px;'>{subject}</p>
            </div>

            <div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;'>
                <p style='margin: 0; font-weight: bold; color: #ffc107;'>💬 Message:</p>
                <div style='margin: 10px 0 0 0; background: white; padding: 15px; border-radius: 4px; white-space: pre-wrap; font-family: Arial, sans-serif;'>{message}</div>
            </div>

            <div style='background: #e8f5e8; padding: 20px; border-radius: 8px; margin: 30px 0; text-align: center;'>
                <h3 style='color: #28a745; margin: 0 0 10px 0; font-size: 18px;'>✅ SMTP Test Successful!</h3>
                <p style='margin: 0; color: #155724;'>If you're reading this, your SMTP configuration is working correctly.</p>
            </div>

            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>

            <div style='text-align: center; color: #6c757d; font-size: 14px;'>
                <p style='margin: 0;'>📅 Sent: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
                <p style='margin: 5px 0 0 0;'>🚀 Powered by VelocityPlatform</p>
            </div>
        </div>
    </div>
</body>
</html>";
        }

        private string GenerateCustomHtmlTemplate(string subject, string message, string? fromName = null)
        {
            var senderName = fromName ?? _emailSettings.FromName;
            var formattedMessage = message.Replace("\n", "<br>");

            return $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <title>{subject}</title>
</head>
<body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4;'>
    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
        <div style='background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;'>
            <h1 style='color: white; margin: 0; font-size: 28px;'>📧 {subject}</h1>
            <p style='color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;'>From {senderName}</p>
        </div>

        <div style='background: white; padding: 40px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);'>
            <div style='margin-bottom: 30px;'>
                {formattedMessage}
            </div>

            <hr style='border: none; border-top: 1px solid #e9ecef; margin: 30px 0;'>

            <div style='text-align: center; color: #6c757d; font-size: 14px;'>
                <p style='margin: 0;'>📅 Sent: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC</p>
                <p style='margin: 5px 0 0 0;'>🚀 Powered by VelocityPlatform</p>
            </div>
        </div>
    </div>
</body>
</html>";
        }

        private async Task SendEmailAsync(string toEmail, string subject, string body, string? customFromName = null)
        {
            try
            {
                using var client = new SmtpClient(_emailSettings.SmtpHost, _emailSettings.SmtpPort);
                client.EnableSsl = _emailSettings.EnableSsl;
                client.UseDefaultCredentials = false;
                client.Credentials = new NetworkCredential(_emailSettings.SmtpUsername, _emailSettings.SmtpPassword);

                // Set delivery method and timeout
                client.DeliveryMethod = SmtpDeliveryMethod.Network;
                client.Timeout = 30000; // 30 seconds

                // Additional SMTP settings for better compatibility
                if (_emailSettings.SmtpPort == 587)
                {
                    // Use STARTTLS for port 587
                    client.EnableSsl = true;
                }
                else if (_emailSettings.SmtpPort == 465)
                {
                    // Use SSL/TLS for port 465
                    client.EnableSsl = true;
                }

                using var message = new MailMessage();
                var fromName = customFromName ?? _emailSettings.FromName;
                message.From = new MailAddress(_emailSettings.FromEmail, fromName);
                message.To.Add(toEmail);
                message.Subject = subject;
                message.Body = body;
                message.IsBodyHtml = true;
                message.BodyEncoding = Encoding.UTF8;
                message.SubjectEncoding = Encoding.UTF8;

                await client.SendMailAsync(message);
                _logger.LogInformation("Email sent successfully to {Email} with subject '{Subject}' from '{FromName}'", toEmail, subject, fromName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email} with subject '{Subject}'", toEmail, subject);

                // Log email content for debugging in case of SMTP failure
                _logger.LogWarning("Email content that failed to send:\nTo: {Email}\nSubject: {Subject}\nBody: {Body}",
                    toEmail, subject, body.Substring(0, Math.Min(500, body.Length)) + "...");

                // For now, don't throw to allow registration to complete
                // In production, you might want to queue the email for retry
                _logger.LogWarning("Email sending failed but registration will continue. Email should be queued for retry.");
            }
        }
    }
}
