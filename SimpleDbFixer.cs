using System;
using System.Threading.Tasks;
using Npgsql;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1";
        
        try
        {
            Console.WriteLine("Starting database fix...");
            
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            
            Console.WriteLine("Connected to database successfully.");
            
            // Add missing columns one by one
            var commands = new[]
            {
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"AvatarUrl\" character varying(500);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"ApiKey\" character varying(100);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"ConfirmationToken\" character varying(500);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"ConfirmationTokenExpiry\" timestamp with time zone;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"ResetToken\" character varying(500);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"ResetTokenExpiry\" timestamp with time zone;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"PreferencesJson\" text;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"ProfileData\" text;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"IsActive\" boolean DEFAULT true;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"CreatedAt\" timestamp with time zone DEFAULT CURRENT_TIMESTAMP;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"UpdatedAt\" timestamp with time zone DEFAULT CURRENT_TIMESTAMP;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"TenantId\" uuid;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"FirstName\" character varying(100);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"LastName\" character varying(100);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"Role\" character varying(50);",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"EmailVerified\" boolean DEFAULT false;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"LastLoginAt\" timestamp with time zone;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"FailedLoginAttempts\" integer DEFAULT 0;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"IsLockedOut\" boolean DEFAULT false;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"LockedUntil\" timestamp with time zone;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"LastPasswordChange\" timestamp with time zone;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"PasswordSalt\" bytea;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"IsAnonymized\" boolean DEFAULT false;",
                "ALTER TABLE \"AspNetUsers\" ADD COLUMN IF NOT EXISTS \"AnonymizedDate\" timestamp with time zone;"
            };
            
            foreach (var sql in commands)
            {
                try
                {
                    using var command = new NpgsqlCommand(sql, connection);
                    await command.ExecuteNonQueryAsync();
                    Console.WriteLine($"✓ Executed: {sql}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"✗ Error executing {sql}: {ex.Message}");
                }
            }
            
            // Update existing users with default values
            var updateSql = @"
                UPDATE ""AspNetUsers"" SET 
                    ""IsActive"" = COALESCE(""IsActive"", true),
                    ""CreatedAt"" = COALESCE(""CreatedAt"", CURRENT_TIMESTAMP),
                    ""UpdatedAt"" = COALESCE(""UpdatedAt"", CURRENT_TIMESTAMP),
                    ""EmailVerified"" = COALESCE(""EmailVerified"", ""EmailConfirmed""),
                    ""FailedLoginAttempts"" = COALESCE(""FailedLoginAttempts"", 0),
                    ""IsLockedOut"" = COALESCE(""IsLockedOut"", false),
                    ""IsAnonymized"" = COALESCE(""IsAnonymized"", false);";
            
            using var updateCommand = new NpgsqlCommand(updateSql, connection);
            var rowsAffected = await updateCommand.ExecuteNonQueryAsync();
            Console.WriteLine($"✓ Updated {rowsAffected} existing user records.");
            
            Console.WriteLine("✅ Database fix completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
