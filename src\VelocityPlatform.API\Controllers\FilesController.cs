using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Data;
using VelocityPlatform.Models.DTOs;
using Microsoft.Extensions.Logging;

namespace VelocityPlatform.API.Controllers
{
    /// <summary>
    /// Controller for file upload and media management
    /// </summary>
    [Route("api/v{version:apiVersion}/Files")]
    [ApiController]
    // [Authorize] // Temporarily disabled for testing
    public class FilesController : BaseController
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly IMediaService _mediaService;

        public FilesController(
            IFileStorageService fileStorageService,
            IMediaService mediaService,
            VelocityPlatformDbContext context,
            ILogger<FilesController> logger,
            ITenantProvider tenantProvider) : base(context, logger, tenantProvider)
        {
            _fileStorageService = fileStorageService;
            _mediaService = mediaService;
        }

        /// <summary>
        /// Get files overview
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<object>>> GetFiles([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var tenantId = GetCurrentTenantId();

                // Return a summary of files
                var summary = new
                {
                    TotalFiles = 0,
                    Categories = new List<string>(),
                    RecentFiles = new List<object>(),
                    StorageUsed = "0 MB",
                    StorageLimit = "1 GB"
                };

                return Ok(ApiResponse(summary, "Files overview retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving files overview");
                return StatusCode(500, ErrorResponse<object>("An error occurred while retrieving files overview."));
            }
        }

        /// <summary>
        /// Upload a file to the media library
        /// </summary>
        [HttpPost("upload")]
        public async Task<ActionResult<ApiResponse<FileUploadResponseDto>>> UploadFile([FromForm] FileUploadDto dto)
        {
            try
            {
                if (dto.File == null || dto.File.Length == 0)
                {
                    return BadRequest(ErrorResponse<FileUploadResponseDto>("No file provided or file is empty."));
                }

                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();

                // Validate file type and size
                var allowedTypes = new[] { ".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".zip" };
                var fileExtension = Path.GetExtension(dto.File.FileName).ToLowerInvariant();
                
                if (!allowedTypes.Contains(fileExtension))
                {
                    return BadRequest(ErrorResponse<FileUploadResponseDto>("File type not allowed."));
                }

                if (dto.File.Length > 10 * 1024 * 1024) // 10MB limit
                {
                    return BadRequest(ErrorResponse<FileUploadResponseDto>("File size exceeds 10MB limit."));
                }

                var uploadResult = await _fileStorageService.UploadFileAsync(dto.File, tenantId, userId, dto.Category);
                
                if (uploadResult == null)
                {
                    return StatusCode(500, ErrorResponse<FileUploadResponseDto>("Failed to upload file."));
                }

                return Ok(ApiResponse(uploadResult, "File uploaded successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file");
                return StatusCode(500, ErrorResponse<FileUploadResponseDto>("An error occurred while uploading the file."));
            }
        }

        /// <summary>
        /// Get media files for the current tenant
        /// </summary>
        [HttpGet("media")]
        public async Task<ActionResult<ApiResponse<PagedResponseDto<MediaFileDto>>>> GetMediaFiles(
            [FromQuery] int pageNumber = 1, 
            [FromQuery] int pageSize = 20,
            [FromQuery] string? category = null,
            [FromQuery] string? searchTerm = null)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var mediaFiles = await _mediaService.GetMediaFilesAsync(tenantId, pageNumber, pageSize, category, searchTerm);
                
                return Ok(ApiResponse(mediaFiles, "Media files retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving media files");
                return StatusCode(500, ErrorResponse<PagedResponseDto<MediaFileDto>>("An error occurred while retrieving media files."));
            }
        }

        /// <summary>
        /// Get a specific media file
        /// </summary>
        [HttpGet("{fileId}")]
        public async Task<ActionResult<ApiResponse<MediaFileDto>>> GetMediaFile(Guid fileId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var mediaFile = await _mediaService.GetMediaFileAsync(fileId, tenantId);
                
                if (mediaFile == null)
                {
                    return NotFound(ErrorResponse<MediaFileDto>("Media file not found."));
                }

                return Ok(ApiResponse(mediaFile, "Media file retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving media file {FileId}", fileId);
                return StatusCode(500, ErrorResponse<MediaFileDto>("An error occurred while retrieving the media file."));
            }
        }

        /// <summary>
        /// Download a file
        /// </summary>
        [HttpGet("{fileId}/download")]
        [AllowAnonymous] // Allow public access for published site assets
        public async Task<IActionResult> DownloadFile(Guid fileId)
        {
            try
            {
                var fileStream = await _fileStorageService.GetFileStreamAsync(fileId);
                
                if (fileStream == null)
                {
                    return NotFound();
                }

                return File(fileStream.Stream, fileStream.ContentType, fileStream.FileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file {FileId}", fileId);
                return StatusCode(500);
            }
        }

        /// <summary>
        /// Delete a media file
        /// </summary>
        [HttpDelete("{fileId}")]
        public async Task<ActionResult<ApiResponse>> DeleteFile(Guid fileId)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                
                var success = await _mediaService.DeleteMediaFileAsync(fileId, tenantId, userId);
                
                if (!success)
                {
                    return NotFound(ErrorResponse("Media file not found or cannot be deleted."));
                }

                return Ok(ApiResponse<object>(null!, "Media file deleted successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting media file {FileId}", fileId);
                return StatusCode(500, ErrorResponse("An error occurred while deleting the media file."));
            }
        }

        /// <summary>
        /// Update media file metadata
        /// </summary>
        [HttpPut("{fileId}")]
        public async Task<ActionResult<ApiResponse<MediaFileDto>>> UpdateMediaFile(Guid fileId, [FromBody] UpdateMediaFileDto dto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var userId = GetCurrentUserId();
                
                var updatedFile = await _mediaService.UpdateMediaFileAsync(fileId, dto, tenantId, userId);
                
                if (updatedFile == null)
                {
                    return NotFound(ErrorResponse<MediaFileDto>("Media file not found."));
                }

                return Ok(ApiResponse(updatedFile, "Media file updated successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating media file {FileId}", fileId);
                return StatusCode(500, ErrorResponse<MediaFileDto>("An error occurred while updating the media file."));
            }
        }

        /// <summary>
        /// Get file categories
        /// </summary>
        [HttpGet("categories")]
        public async Task<ActionResult<ApiResponse<IEnumerable<string>>>> GetFileCategories()
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var categories = await _mediaService.GetFileCategoriesAsync(tenantId);
                
                return Ok(ApiResponse(categories, "File categories retrieved successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving file categories");
                return StatusCode(500, ErrorResponse<IEnumerable<string>>("An error occurred while retrieving file categories."));
            }
        }

        /// <summary>
        /// Generate optimized image variants
        /// </summary>
        [HttpPost("{fileId}/optimize")]
        public async Task<ActionResult<ApiResponse<ImageOptimizationResultDto>>> OptimizeImage(Guid fileId, [FromBody] ImageOptimizationRequestDto dto)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                var result = await _mediaService.OptimizeImageAsync(fileId, dto, tenantId);
                
                if (result == null)
                {
                    return NotFound(ErrorResponse<ImageOptimizationResultDto>("Image not found or optimization failed."));
                }

                return Ok(ApiResponse(result, "Image optimized successfully."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error optimizing image {FileId}", fileId);
                return StatusCode(500, ErrorResponse<ImageOptimizationResultDto>("An error occurred while optimizing the image."));
            }
        }
    }
}
