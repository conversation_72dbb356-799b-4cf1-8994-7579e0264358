using VelocityPlatform.Models.DTOs;
using System.Text.Json;

namespace VelocityPlatform.Business.Services
{
    /// <summary>
    /// Implementation of component template service
    /// </summary>
    public class ComponentTemplateService : IComponentTemplateService
    {
        public async Task<IEnumerable<ComponentTemplateDto>> GetComponentTemplatesAsync(string? category = null)
        {
            // Simulate template retrieval
            await Task.Delay(100);
            
            var templates = new List<ComponentTemplateDto>
            {
                new ComponentTemplateDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Hero Section",
                    Description = "A responsive hero section with title, subtitle, and call-to-action button",
                    Category = category ?? "layout",
                    PreviewImageUrl = "/templates/hero-section-preview.jpg",
                    HtmlTemplate = @"
                        <section class='hero-section'>
                            <div class='hero-content'>
                                <h1 class='hero-title'>{{title}}</h1>
                                <p class='hero-subtitle'>{{subtitle}}</p>
                                <button class='hero-cta'>{{ctaText}}</button>
                            </div>
                        </section>",
                    CssStyles = @"
                        .hero-section { 
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 4rem 2rem;
                            text-align: center;
                        }
                        .hero-title { font-size: 3rem; margin-bottom: 1rem; }
                        .hero-subtitle { font-size: 1.2rem; margin-bottom: 2rem; }
                        .hero-cta { 
                            background: #ff6b6b;
                            color: white;
                            padding: 1rem 2rem;
                            border: none;
                            border-radius: 5px;
                            font-size: 1.1rem;
                            cursor: pointer;
                        }",
                    JavaScriptCode = @"
                        document.querySelector('.hero-cta').addEventListener('click', function() {
                            console.log('Hero CTA clicked');
                        });",
                    ConfigurationSchema = JsonDocument.Parse(@"{
                        ""type"": ""object"",
                        ""properties"": {
                            ""title"": { ""type"": ""string"", ""default"": ""Welcome to Our Platform"" },
                            ""subtitle"": { ""type"": ""string"", ""default"": ""Build amazing websites with ease"" },
                            ""ctaText"": { ""type"": ""string"", ""default"": ""Get Started"" }
                        }
                    }"),
                    DefaultConfiguration = JsonDocument.Parse(@"{
                        ""title"": ""Welcome to Our Platform"",
                        ""subtitle"": ""Build amazing websites with ease"",
                        ""ctaText"": ""Get Started""
                    }"),
                    IsPremium = false
                },
                new ComponentTemplateDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Feature Grid",
                    Description = "A responsive grid layout for showcasing features or services",
                    Category = category ?? "content",
                    PreviewImageUrl = "/templates/feature-grid-preview.jpg",
                    HtmlTemplate = @"
                        <section class='feature-grid'>
                            <div class='container'>
                                <h2 class='grid-title'>{{title}}</h2>
                                <div class='features'>
                                    {{#each features}}
                                    <div class='feature-item'>
                                        <div class='feature-icon'>{{icon}}</div>
                                        <h3 class='feature-title'>{{title}}</h3>
                                        <p class='feature-description'>{{description}}</p>
                                    </div>
                                    {{/each}}
                                </div>
                            </div>
                        </section>",
                    CssStyles = @"
                        .feature-grid { padding: 4rem 2rem; }
                        .container { max-width: 1200px; margin: 0 auto; }
                        .grid-title { text-align: center; margin-bottom: 3rem; font-size: 2.5rem; }
                        .features { 
                            display: grid; 
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); 
                            gap: 2rem; 
                        }
                        .feature-item { 
                            text-align: center; 
                            padding: 2rem; 
                            border-radius: 10px; 
                            box-shadow: 0 4px 6px rgba(0,0,0,0.1); 
                        }
                        .feature-icon { font-size: 3rem; margin-bottom: 1rem; }
                        .feature-title { margin-bottom: 1rem; color: #333; }
                        .feature-description { color: #666; line-height: 1.6; }",
                    JavaScriptCode = @"
                        // Add hover effects
                        document.querySelectorAll('.feature-item').forEach(item => {
                            item.addEventListener('mouseenter', function() {
                                this.style.transform = 'translateY(-5px)';
                                this.style.transition = 'transform 0.3s ease';
                            });
                            item.addEventListener('mouseleave', function() {
                                this.style.transform = 'translateY(0)';
                            });
                        });",
                    ConfigurationSchema = JsonDocument.Parse(@"{
                        ""type"": ""object"",
                        ""properties"": {
                            ""title"": { ""type"": ""string"", ""default"": ""Our Features"" },
                            ""features"": {
                                ""type"": ""array"",
                                ""items"": {
                                    ""type"": ""object"",
                                    ""properties"": {
                                        ""icon"": { ""type"": ""string"", ""default"": ""🚀"" },
                                        ""title"": { ""type"": ""string"", ""default"": ""Feature Title"" },
                                        ""description"": { ""type"": ""string"", ""default"": ""Feature description"" }
                                    }
                                }
                            }
                        }
                    }"),
                    DefaultConfiguration = JsonDocument.Parse(@"{
                        ""title"": ""Our Features"",
                        ""features"": [
                            { ""icon"": ""🚀"", ""title"": ""Fast Performance"", ""description"": ""Lightning fast loading times"" },
                            { ""icon"": ""🔒"", ""title"": ""Secure"", ""description"": ""Enterprise-grade security"" },
                            { ""icon"": ""📱"", ""title"": ""Responsive"", ""description"": ""Works on all devices"" }
                        ]
                    }"),
                    IsPremium = false
                },
                new ComponentTemplateDto
                {
                    Id = Guid.NewGuid(),
                    Name = "Contact Form",
                    Description = "A professional contact form with validation",
                    Category = category ?? "forms",
                    PreviewImageUrl = "/templates/contact-form-preview.jpg",
                    HtmlTemplate = @"
                        <section class='contact-form-section'>
                            <div class='container'>
                                <h2 class='form-title'>{{title}}</h2>
                                <form class='contact-form' id='contactForm'>
                                    <div class='form-group'>
                                        <label for='name'>Name</label>
                                        <input type='text' id='name' name='name' required>
                                    </div>
                                    <div class='form-group'>
                                        <label for='email'>Email</label>
                                        <input type='email' id='email' name='email' required>
                                    </div>
                                    <div class='form-group'>
                                        <label for='message'>Message</label>
                                        <textarea id='message' name='message' rows='5' required></textarea>
                                    </div>
                                    <button type='submit' class='submit-btn'>{{submitText}}</button>
                                </form>
                            </div>
                        </section>",
                    CssStyles = @"
                        .contact-form-section { padding: 4rem 2rem; background: #f8f9fa; }
                        .container { max-width: 600px; margin: 0 auto; }
                        .form-title { text-align: center; margin-bottom: 2rem; }
                        .contact-form { background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
                        .form-group { margin-bottom: 1.5rem; }
                        .form-group label { display: block; margin-bottom: 0.5rem; font-weight: bold; }
                        .form-group input, .form-group textarea { 
                            width: 100%; 
                            padding: 0.75rem; 
                            border: 1px solid #ddd; 
                            border-radius: 5px; 
                            font-size: 1rem; 
                        }
                        .submit-btn { 
                            background: #007bff; 
                            color: white; 
                            padding: 1rem 2rem; 
                            border: none; 
                            border-radius: 5px; 
                            cursor: pointer; 
                            width: 100%; 
                            font-size: 1.1rem; 
                        }
                        .submit-btn:hover { background: #0056b3; }",
                    JavaScriptCode = @"
                        document.getElementById('contactForm').addEventListener('submit', function(e) {
                            e.preventDefault();
                            
                            // Basic validation
                            const name = document.getElementById('name').value;
                            const email = document.getElementById('email').value;
                            const message = document.getElementById('message').value;
                            
                            if (name && email && message) {
                                alert('Thank you for your message! We will get back to you soon.');
                                this.reset();
                            } else {
                                alert('Please fill in all fields.');
                            }
                        });",
                    ConfigurationSchema = JsonDocument.Parse(@"{
                        ""type"": ""object"",
                        ""properties"": {
                            ""title"": { ""type"": ""string"", ""default"": ""Contact Us"" },
                            ""submitText"": { ""type"": ""string"", ""default"": ""Send Message"" }
                        }
                    }"),
                    DefaultConfiguration = JsonDocument.Parse(@"{
                        ""title"": ""Contact Us"",
                        ""submitText"": ""Send Message""
                    }"),
                    IsPremium = true,
                    Price = 9.99m
                }
            };
            
            return category != null ? templates.Where(t => t.Category == category) : templates;
        }

        public async Task<ComponentTemplateDto?> GetComponentTemplateAsync(Guid templateId)
        {
            // Simulate template retrieval
            await Task.Delay(50);
            
            var templates = await GetComponentTemplatesAsync();
            return templates.FirstOrDefault(t => t.Id == templateId);
        }

        public async Task<ComponentTemplateDto?> CreateComponentTemplateAsync(CreateComponentTemplateDto createDto, Guid userId)
        {
            // Simulate template creation
            await Task.Delay(100);
            
            return new ComponentTemplateDto
            {
                Id = Guid.NewGuid(),
                Name = createDto.Name,
                Description = createDto.Description,
                Category = createDto.Category,
                PreviewImageUrl = createDto.PreviewImageUrl,
                HtmlTemplate = createDto.HtmlTemplate,
                CssStyles = createDto.CssStyles,
                JavaScriptCode = createDto.JavaScriptCode,
                IsPremium = createDto.IsPremium,
                Price = createDto.Price
            };
        }

        public async Task<ComponentTemplateDto?> UpdateComponentTemplateAsync(Guid templateId, UpdateComponentTemplateDto updateDto, Guid userId)
        {
            // Simulate template update
            await Task.Delay(50);
            
            return new ComponentTemplateDto
            {
                Id = templateId,
                Name = updateDto.Name ?? "Updated Template",
                Description = updateDto.Description ?? "Updated description",
                Category = updateDto.Category ?? "layout",
                PreviewImageUrl = updateDto.PreviewImageUrl,
                HtmlTemplate = updateDto.HtmlTemplate ?? "<div>Updated template</div>",
                CssStyles = updateDto.CssStyles ?? ".updated { color: blue; }",
                JavaScriptCode = updateDto.JavaScriptCode ?? "console.log('Updated template');",
                IsPremium = updateDto.IsPremium ?? false,
                Price = updateDto.Price
            };
        }

        public async Task<bool> DeleteComponentTemplateAsync(Guid templateId, Guid userId)
        {
            // Simulate template deletion
            await Task.Delay(50);
            return true;
        }

        public async Task<IEnumerable<string>> GetTemplateCategoriesAsync()
        {
            // Simulate category retrieval
            await Task.Delay(50);
            
            return new[] { "layout", "content", "forms", "navigation", "media", "ecommerce" };
        }

        public async Task<IEnumerable<ComponentTemplateDto>> SearchComponentTemplatesAsync(string searchQuery, string? category = null)
        {
            // Simulate search
            await Task.Delay(100);
            
            var allTemplates = await GetComponentTemplatesAsync(category);
            return allTemplates.Where(t => 
                t.Name.Contains(searchQuery, StringComparison.OrdinalIgnoreCase) ||
                t.Description.Contains(searchQuery, StringComparison.OrdinalIgnoreCase));
        }
    }
}
