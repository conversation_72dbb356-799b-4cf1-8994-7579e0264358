using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Models.Enums;
using VelocityPlatform.Security;

namespace VelocityPlatform.Data;

public class VelocityPlatformDbContext : IdentityDbContext<User, IdentityRole<Guid>, Guid>
{
    private readonly ITenantProvider _tenantProvider;

    public VelocityPlatformDbContext(DbContextOptions<VelocityPlatformDbContext> options, ITenantProvider tenantProvider)
        : base(options)
    {
        _tenantProvider = tenantProvider;
    }

    public virtual DbSet<AddonInstance> AddonInstances { get; set; }
    public virtual DbSet<AddonDefinition> AddonDefinitions { get; set; }
    public virtual DbSet<AddonDraft> AddonDrafts { get; set; }
    public virtual DbSet<AddonVersion> AddonVersions { get; set; }
    public virtual DbSet<AddonPurchase> AddonPurchases { get; set; }
    public virtual DbSet<ApiEndpoint> ApiEndpoints { get; set; }
    public virtual DbSet<AuditLog> AuditLogs { get; set; }
    public virtual DbSet<DataProcessingLog> DataProcessingLogs { get; set; }
    public virtual DbSet<DeploymentArtifact> DeploymentArtifacts { get; set; }
    public virtual DbSet<EncryptionKey> EncryptionKeys { get; set; }
    public virtual DbSet<GDPRReport> GDPRReports { get; set; }
    public virtual DbSet<Invoice> Invoices { get; set; }
    public virtual DbSet<IsolationPolicy> IsolationPolicies { get; set; }
    public virtual DbSet<Page> Pages { get; set; }
    public virtual DbSet<PageSnippetInstance> PageSnippetInstances { get; set; }
    public virtual DbSet<PageVersion> PageVersions { get; set; }
    public virtual DbSet<PageTemplate> PageTemplates { get; set; }
    public virtual DbSet<PredefinedSnippet> PredefinedSnippets { get; set; }
    public virtual DbSet<PredefinedSnippetVersion> PredefinedSnippetVersions { get; set; }
    public virtual DbSet<RefreshToken> RefreshTokens { get; set; }
    public virtual DbSet<SecurityEvent> SecurityEvents { get; set; }
    public virtual DbSet<Site> Sites { get; set; }
    public virtual DbSet<SiteCompilationResult> SiteCompilationResults { get; set; }
    public virtual DbSet<SiteVersion> SiteVersions { get; set; }
    public virtual DbSet<SubscriptionPlan> SubscriptionPlans { get; set; }
    public virtual DbSet<SystemConfiguration> SystemConfigurations { get; set; }
    public virtual DbSet<Tenant> Tenants { get; set; }
    public virtual DbSet<User> Users { get; set; }
    public virtual DbSet<UserConsent> UserConsents { get; set; }
    public virtual DbSet<VelocityPlatform.Models.Entities.UserRole> UserRoles { get; set; }
    public virtual DbSet<UserSubscription> UserSubscriptions { get; set; }
    // public virtual DbSet<Payment> Payments { get; set; } // Commented out - table doesn't exist in database
    public virtual DbSet<VulnerabilityScan> VulnerabilityScans { get; set; }
    public virtual DbSet<VulnerabilityScanResult> VulnerabilityScanResults { get; set; }
    public virtual DbSet<Consent> Consents { get; set; } // Added DbSet for Consent

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        // Configure User-Tenant relationship
        modelBuilder.Entity<User>()
            .HasOne(u => u.Tenant)
            .WithMany(t => t.Users)
            .HasForeignKey(u => u.TenantId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure Tenant entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<Tenant>(entity =>
        {
            entity.Ignore(t => t.CreatedByUserId);
            entity.Ignore(t => t.LastModifiedByUserId);
            entity.Ignore(t => t.Domain); // Domain column doesn't exist in DB
            entity.Ignore(t => t.Status); // Status column doesn't exist in DB
            entity.Ignore(t => t.Configuration); // Navigation property
            entity.Ignore(t => t.IsolationPolicies); // Navigation property
            entity.Ignore(t => t.Sites); // Navigation property
        });

        // Configure Site entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<Site>(entity =>
        {
            entity.Ignore(s => s.CreatedByUserId);
            entity.Ignore(s => s.LastModifiedByUserId);
            entity.Ignore(s => s.ApprovalStatus); // ApprovalStatus column doesn't exist in DB
            entity.Ignore(s => s.ReviewRequestedAt); // ReviewRequestedAt column doesn't exist in DB
            entity.Ignore(s => s.ReviewerId); // ReviewerId column doesn't exist in DB
            entity.Ignore(s => s.ScheduledPublishAt); // ScheduledPublishAt column doesn't exist in DB
            entity.Ignore(s => s.LastDeploymentDate); // LastDeploymentDate column doesn't exist in DB
            entity.Ignore(s => s.LastCompilationDate); // LastCompilationDate column doesn't exist in DB
            entity.Ignore(s => s.LastCompilationStatus); // LastCompilationStatus column doesn't exist in DB
            entity.Ignore(s => s.PageHierarchy); // PageHierarchy column doesn't exist in DB
            entity.Ignore(s => s.Tenant); // Navigation property
            entity.Ignore(s => s.Owner); // Navigation property
            entity.Ignore(s => s.Reviewer); // Navigation property
            entity.Ignore(s => s.Pages); // Navigation property
            entity.Ignore(s => s.Versions); // Navigation property
            entity.Ignore(s => s.CurrentVersion); // Navigation property
        });

        // Configure AddonDefinition entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<AddonDefinition>(entity =>
        {
            entity.Ignore(a => a.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(a => a.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
            entity.Ignore(a => a.UserId); // UserId column doesn't exist in DB
            entity.Ignore(a => a.Tenant); // Navigation property
            entity.Ignore(a => a.Creator); // Navigation property
            entity.Ignore(a => a.Approver); // Navigation property
            entity.Ignore(a => a.Versions); // Navigation property
            entity.Ignore(a => a.CurrentVersion); // Navigation property
            entity.Ignore(a => a.Instances); // Navigation property
        });

        // Configure User entity to ignore navigation properties that cause relationship issues
        modelBuilder.Entity<User>(entity =>
        {
            entity.Ignore(u => u.CreatedAddons); // Navigation property that causes UserId1 shadow property
        });

        // Configure Page entity to map to actual database schema
        modelBuilder.Entity<Page>(entity =>
        {
            // Map entity properties to actual database column names
            entity.Property(p => p.CurrentPageVersionId).HasColumnName("CurrentVersionId");
            entity.Property(p => p.Order).HasColumnName("SortOrder");

            // Ignore navigation properties that don't exist in DB
            entity.Ignore(p => p.CurrentPageVersion); // Navigation property
            entity.Ignore(p => p.Versions); // Navigation property
            entity.Ignore(p => p.Site); // Navigation property
            entity.Ignore(p => p.PageSnippetInstances); // Navigation property

            // Ignore BaseEntity properties that don't exist in Pages table
            entity.Ignore(p => p.CreatedByUserId); // CreatedByUserId column doesn't exist in Pages table
            entity.Ignore(p => p.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in Pages table
        });

        // Ignore Payment entity - table doesn't exist in database
        modelBuilder.Ignore<Payment>();

        // Configure UserSubscription entity to ignore missing columns
        modelBuilder.Entity<UserSubscription>(entity =>
        {
            // Ignore BaseEntity properties that don't exist in UserSubscriptions table
            entity.Ignore(us => us.CreatedByUserId); // CreatedByUserId column doesn't exist in UserSubscriptions table
            entity.Ignore(us => us.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in UserSubscriptions table
            entity.Ignore(us => us.NextBillingDate); // NextBillingDate column doesn't exist in UserSubscriptions table

            // Ignore navigation properties
            entity.Ignore(us => us.User); // Navigation property
            entity.Ignore(us => us.SubscriptionPlan); // Navigation property
        });

        // Configure PredefinedSnippet entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<PredefinedSnippet>(entity =>
        {
            entity.Ignore(p => p.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(p => p.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
            entity.Ignore(p => p.Creator); // Navigation property
            entity.Ignore(p => p.Versions); // Navigation property
            entity.Ignore(p => p.CurrentVersion); // Navigation property
            entity.Ignore(p => p.PageInstances); // Navigation property
        });

        // Configure AddonVersion entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<AddonVersion>(entity =>
        {
            entity.Ignore(a => a.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(a => a.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
            entity.Ignore(a => a.AddonConfiguration); // NotMapped property
            entity.Ignore(a => a.SchemaDefinition); // NotMapped property
        });

        // Configure Page entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<Page>(entity =>
        {
            entity.Ignore(p => p.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(p => p.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
        });

        // Configure PageVersion entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<PageVersion>(entity =>
        {
            entity.Ignore(p => p.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(p => p.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
        });

        // Configure PageSnippetInstance entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<PageSnippetInstance>(entity =>
        {
            entity.Ignore(p => p.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(p => p.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
            entity.Ignore(p => p.Tenant); // Navigation property
            entity.Ignore(p => p.Page); // Navigation property
            entity.Ignore(p => p.Snippet); // Navigation property
            entity.Ignore(p => p.SnippetVersion); // Navigation property
        });

        // Configure ApiEndpoint entity to ignore missing columns and map to actual DB schema
        modelBuilder.Entity<ApiEndpoint>(entity =>
        {
            entity.Ignore(a => a.CreatedByUserId); // CreatedByUserId column doesn't exist in DB
            entity.Ignore(a => a.LastModifiedByUserId); // LastModifiedByUserId column doesn't exist in DB
            entity.Ignore(a => a.Tenant); // Navigation property
            entity.Ignore(a => a.AddonInstance); // Navigation property
        });
        
        modelBuilder.Ignore<System.Text.Json.JsonDocument>();
        
        // Configure SiteCompilationResult primary key
        modelBuilder.Entity<SiteCompilationResult>(entity => 
        {
            entity.HasKey(e => e.Id);
        });

        // Configure AddonDefinition relationships
        modelBuilder.Entity<AddonDefinition>()
            .HasOne(ad => ad.Approver)
            .WithMany()
            .HasForeignKey(ad => ad.ApprovedBy)
            .IsRequired(false);
            
        // Add relationship configurations for entities with CurrentVersion
        modelBuilder.Entity<PredefinedSnippet>(entity => 
        {
            entity.HasOne(p => p.CurrentVersion)
                  .WithOne()
                  .HasForeignKey<PredefinedSnippet>(p => p.CurrentVersionId)
                  .IsRequired(false);
        });
        
        modelBuilder.Entity<Site>(entity =>
        {
            entity.HasOne(s => s.CurrentVersion)
                  .WithOne()
                  .HasForeignKey<Site>(s => s.CurrentVersionId)
                  .IsRequired(false);

            // Configure the Owner relationship (Site to User)
            entity.HasOne(s => s.Owner)
                  .WithMany(u => u.OwnedSites) // This links to the User.OwnedSites collection
                  .HasForeignKey(s => s.OwnerId)
                  .OnDelete(DeleteBehavior.Restrict); // Prevents deleting a User if they own Sites

            // Note: Reviewer relationship removed because ReviewerId column doesn't exist in database
        });
        
        modelBuilder.Entity<AddonDefinition>(entity => 
        {
            entity.HasOne(a => a.CurrentVersion)
                  .WithOne()
                  .HasForeignKey<AddonDefinition>(a => a.CurrentVersionId)
                  .IsRequired(false);
        });
        
        modelBuilder.Entity<Page>(entity => 
        {
            entity.HasOne(p => p.CurrentPageVersion) // Corrected to CurrentPageVersion
                  .WithOne()
                  .HasForeignKey<Page>(p => p.CurrentPageVersionId) // Corrected to CurrentPageVersionId
                  .IsRequired(false);
        });
    }
}
