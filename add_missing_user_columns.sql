-- Add missing columns to Asp<PERSON><PERSON><PERSON>s table
-- These columns are expected by the User entity but missing from the database

-- Add AvatarUrl column
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "AvatarUrl" character varying(500);

-- Add other missing columns that might be needed
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ApiKey" character varying(100);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ConfirmationToken" character varying(500);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ConfirmationTokenExpiry" timestamp with time zone;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ResetToken" character varying(500);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ResetTokenExpiry" timestamp with time zone;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "PreferencesJson" text;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ProfileData" text;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "IsActive" boolean DEFAULT true;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "CreatedAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "UpdatedAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "TenantId" uuid;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "FirstName" character varying(100);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LastName" character varying(100);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "Role" character varying(50);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "EmailVerified" boolean DEFAULT false;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LastLoginAt" timestamp with time zone;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "FailedLoginAttempts" integer DEFAULT 0;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "IsLockedOut" boolean DEFAULT false;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LockedUntil" timestamp with time zone;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LastPasswordChange" timestamp with time zone;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "PasswordSalt" character varying(100);
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "IsAnonymized" boolean DEFAULT false;
ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "AnonymizedDate" timestamp with time zone;

-- Update existing users to have default values
UPDATE "AspNetUsers" SET 
    "IsActive" = true,
    "CreatedAt" = CURRENT_TIMESTAMP,
    "UpdatedAt" = CURRENT_TIMESTAMP,
    "EmailVerified" = "EmailConfirmed",
    "FailedLoginAttempts" = 0,
    "IsLockedOut" = false,
    "IsAnonymized" = false
WHERE "IsActive" IS NULL;
