using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;
using Microsoft.Extensions.Logging;
using System;
using System.Security.Claims;
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.DTOs; // Ensure all necessary DTOs are here
using System.Linq; // Added for ModelState.Values.SelectMany
using System.ComponentModel.DataAnnotations;

namespace VelocityPlatform.API.Controllers
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    [ApiController] // Added for consistency and common API behaviors
    public class AuthController : ControllerBase
    {
        private readonly IAuthService _authService;
        private readonly IApiKeyService _apiKeyService;
        private readonly IUserService _userService; // Retained for CheckUserExists, GetMe could also use it if UserProfileDto needs more than User entity from AuthSvc
        private readonly IEmailService _emailService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IAuthService authService,
            IApiKeyService apiKeyService,
            IUserService userService,
            IEmailService emailService,
            ILogger<AuthController> logger)
        {
            _authService = authService ?? throw new ArgumentNullException(nameof(authService));
            _apiKeyService = apiKeyService ?? throw new ArgumentNullException(nameof(apiKeyService));
            _userService = userService ?? throw new ArgumentNullException(nameof(userService));
            _emailService = emailService ?? throw new ArgumentNullException(nameof(emailService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Retrieves current user profile.
        /// </summary>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<UserProfileDto>>> GetMe()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out Guid userId))
            {
                _logger.LogWarning("GetMe: User ID claim is missing, invalid, or not a Guid.");
                return Unauthorized(ApiResponse<UserProfileDto>.FailureResponse("User ID not found or invalid in token."));
            }

            var userEntity = await _authService.GetUserByIdAsync(userId);
            if (userEntity == null)
            {
                _logger.LogWarning("GetMe: User not found for ID {UserId}", userId);
                return NotFound(ApiResponse<UserProfileDto>.FailureResponse("User profile not found."));
            }

            var userProfile = new UserProfileDto
            {
                Id = userEntity.Id,
                UserId = userEntity.Id,
                UserName = userEntity.UserName ?? string.Empty,
                FirstName = userEntity.FirstName ?? string.Empty,
                LastName = userEntity.LastName ?? string.Empty,
                Email = userEntity.Email ?? string.Empty,
                TenantId = userEntity.TenantId,
                Role = userEntity.Role
            };

            return Ok(ApiResponse<UserProfileDto>.SuccessResponse(userProfile, "User profile retrieved successfully."));
        }

        /// <summary>
        /// Generates/retrieves API key for the authenticated user.
        /// </summary>
        [HttpGet("api-key")]
        [Authorize]
        [EnableRateLimiting("LoginAndApiKey")]
        public async Task<ActionResult<ApiResponse<ApiKeyResponseDto>>> GetApiKey()
        {
            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out Guid userId)) // Ensure it's a Guid if service expects Guid
            {
                _logger.LogWarning("GetApiKey: User ID claim is missing or invalid.");
                return Unauthorized(ApiResponse<ApiKeyResponseDto>.FailureResponse("User ID not found or invalid in token."));
            }

            // Assuming IApiKeyService.GetApiKeyAsync might take Guid or string. Adjust if necessary.
            // If it takes Guid, pass userId. If string, pass userIdClaim.
            // For this example, let's assume it takes the string claim.
            var apiKeyResponse = await _apiKeyService.GetApiKeyAsync(userIdClaim);
            if (apiKeyResponse == null)
            {
                _logger.LogWarning("GetApiKey: API key not found or could not be generated for user ID {UserIdClaim}", userIdClaim);
                return NotFound(ApiResponse<ApiKeyResponseDto>.FailureResponse("API key could not be retrieved or generated."));
            }

            return Ok(ApiResponse<ApiKeyResponseDto>.SuccessResponse(apiKeyResponse, "API key retrieved successfully."));
        }

        /// <summary>
        /// Logs in a user.
        /// </summary>
        [HttpPost("login")]
        [AllowAnonymous]
        [EnableRateLimiting("LoginAndApiKey")]
        public async Task<ActionResult<ApiResponse<AuthenticationResult>>> Login([FromBody] LoginRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<AuthenticationResult>.FailureResponse(string.Join(" ", errors)));
            }

            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            _logger.LogInformation("Login attempt for {Email} from IP {IPAddress}", request.Email, ipAddress);
            var authResult = await _authService.LoginAsync(request, ipAddress, userAgent);

            if (!authResult.Success)
            {
                _logger.LogWarning("Login failed for {Email}: {ErrorMessage}", request.Email, authResult.ErrorMessage);
                // Return Unauthorized for login failures, but BadRequest if it's a known "bad" state like email not confirmed
                if (authResult.ErrorMessage != null && authResult.ErrorMessage.Contains("Email not confirmed"))
                {
                    return BadRequest(ApiResponse<AuthenticationResult>.FailureResponse(authResult.ErrorMessage));
                }
                return Unauthorized(ApiResponse<AuthenticationResult>.FailureResponse(authResult.ErrorMessage ?? "Login failed."));
            }

            _logger.LogInformation("Login successful for {Email}", request.Email);
            return Ok(ApiResponse<AuthenticationResult>.SuccessResponse(authResult, "Login successful."));
        }

        /// <summary>
        /// Registers a new user.
        /// </summary>
        [HttpPost("register")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<AuthenticationResult>>> Register([FromBody] RegisterRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<AuthenticationResult>.FailureResponse(string.Join(" ", errors)));
            }

            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            _logger.LogInformation("Registration attempt for {Email}", request.Email);
            var authResult = await _authService.RegisterAsync(request, ipAddress, userAgent);

            if (!authResult.Success)
            {
                _logger.LogWarning("Registration failed for {Email}: {ErrorMessage}", request.Email, authResult.ErrorMessage);
                return BadRequest(ApiResponse<AuthenticationResult>.FailureResponse(authResult.ErrorMessage ?? "Registration failed."));
            }

            _logger.LogInformation("Registration successful for {Email}. Please check email for confirmation.", request.Email);
            // Consider returning 201 Created with a location header if applicable
            if (authResult.User != null)
            {
                return CreatedAtAction(nameof(GetMe), new { }, ApiResponse<AuthenticationResult>.SuccessResponse(authResult, "Registration successful. Please check your email to confirm your account."));
            }
            return Ok(ApiResponse<AuthenticationResult>.SuccessResponse(authResult, "Registration successful. Please check your email to confirm your account."));
        }

        /// <summary>
        /// Refreshes an authentication token.
        /// </summary>
        [HttpPost("refresh-token")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<TokenResponse>>> RefreshToken([FromBody] RefreshTokenRequestDto request)
        {
            if (string.IsNullOrEmpty(request?.RefreshToken))
            {
                return BadRequest(ApiResponse<TokenResponse>.FailureResponse("Refresh token is required."));
            }

            var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            var userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            _logger.LogInformation("Refresh token attempt.");
            var tokenResponse = await _authService.RefreshTokenAsync(request.RefreshToken, ipAddress, userAgent);

            if (tokenResponse == null)
            {
                _logger.LogWarning("Refresh token failed or token was invalid.");
                return Unauthorized(ApiResponse<TokenResponse>.FailureResponse("Invalid or expired refresh token."));
            }

            _logger.LogInformation("Token refreshed successfully.");
            return Ok(ApiResponse<TokenResponse>.SuccessResponse(tokenResponse, "Token refreshed successfully."));
        }
        
        /// <summary>
        /// Logs out a user by revoking the provided refresh token.
        /// </summary>
        [HttpPost("logout")]
        [Authorize] // Should be authorized to identify user context if revoking all tokens, or allow anonymous if only revoking specific token
        public async Task<ActionResult<ApiResponse<object>>> Logout([FromBody] LogoutRequestDto request)
        {
            if (string.IsNullOrEmpty(request?.RefreshToken))
            {
                 // If no refresh token is provided, try to get user ID from claims and revoke all their tokens.
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (Guid.TryParse(userIdClaim, out Guid userId))
                {
                    var ipAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
                    await _authService.RevokeAllUserRefreshTokensAsync(userId, ipAddress, "User initiated logout");
                    _logger.LogInformation("User {UserId} logged out by revoking all tokens.", userId);
                    return Ok(ApiResponse.SuccessResponse(message: "Successfully logged out. All sessions invalidated."));
                }
                // If no token and no user claim, can't do much.
                _logger.LogWarning("Logout attempt with no refresh token and no user context.");
                return BadRequest(ApiResponse.FailureResponse("Refresh token is required for this logout method or user must be authenticated."));
            }

            // If a refresh token is provided, attempt to revoke it specifically.
            // Note: IAuthService.LogoutAsync was defined with `string refreshToken`.
            // If it's meant to be more comprehensive, it might need IP, user agent, or user context.
            // For now, assuming it just revokes the specific token.
            var ipForRevoke = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
            await _authService.RevokeRefreshTokenAsync(request.RefreshToken, ipForRevoke, "User initiated logout");
            // LogoutAsync in IAuthService returns bool, but here we just perform action.
            // The original controller had `_authService.LogoutAsync(refreshToken)` which returned bool.
            // The new IAuthService has `RevokeRefreshTokenAsync` (void) and `RevokeAllUserRefreshTokensAsync` (void).
            // And `LogoutAsync(string refreshToken)` which returns bool. Let's stick to `LogoutAsync` if it's preferred for a simple logout.
            // Re-checking IAuthService: `Task<bool> LogoutAsync(string refreshToken);` - This is simpler.

            bool logoutSuccess = await _authService.LogoutAsync(request.RefreshToken);
            if(logoutSuccess)
            {
                _logger.LogInformation("Logout successful for token.");
                 return Ok(ApiResponse.SuccessResponse(message: "Successfully logged out."));
            }
            _logger.LogWarning("Logout failed for the provided token (it might have been invalid or already revoked).");
            return Ok(ApiResponse.SuccessResponse(message: "Logout processed. If the token was valid, it has been revoked.")); // Still return OK not to leak info.
        }


        /// <summary>
        /// Initiates the password forgot process for a user.
        /// </summary>
        [HttpPost("forgot-password")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> ForgotPassword([FromBody] ForgotPasswordRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse.FailureResponse(string.Join(" ", errors)));
            }

            _logger.LogInformation("Forgot password request for {Email}", request.Email);
            await _authService.ForgotPasswordAsync(request);

            // Always return a positive-sounding message to prevent email enumeration
            return Ok(ApiResponse.SuccessResponse(message: "If an account with that email exists, a password reset link has been sent."));
        }

        /// <summary>
        /// Resets a user's password using a token.
        /// </summary>
        [HttpPost("reset-password")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> ResetPassword([FromBody] ResetPasswordRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse.FailureResponse(string.Join(" ", errors)));
            }

            _logger.LogInformation("Reset password attempt for {Email}", request.Email);
            var success = await _authService.ResetPasswordAsync(request);

            if (!success)
            {
                _logger.LogWarning("Password reset failed for {Email}, token might be invalid or expired.", request.Email);
                return BadRequest(ApiResponse.FailureResponse("Password reset failed. The token may be invalid or expired."));
            }

            _logger.LogInformation("Password reset successful for {Email}", request.Email);
            return Ok(ApiResponse.SuccessResponse(message: "Password has been reset successfully."));
        }

        /// <summary>
        /// Changes the password for an authenticated user.
        /// </summary>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<object>>> ChangePassword([FromBody] ChangePasswordRequest request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse.FailureResponse(string.Join(" ", errors)));
            }

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !Guid.TryParse(userIdClaim, out Guid userId))
            {
                _logger.LogWarning("ChangePassword: User ID claim is missing or invalid.");
                return Unauthorized(ApiResponse.FailureResponse("User ID not found or invalid in token."));
            }

            _logger.LogInformation("Change password attempt for user {UserId}", userId);
            var success = await _authService.ChangePasswordAsync(userId, request);

            if (!success)
            {
                _logger.LogWarning("Change password failed for user {UserId}. Current password might be incorrect.", userId);
                return BadRequest(ApiResponse.FailureResponse("Failed to change password. Please ensure your current password is correct."));
            }

            _logger.LogInformation("Password changed successfully for user {UserId}", userId);
            return Ok(ApiResponse.SuccessResponse(message: "Password changed successfully."));
        }

        /// <summary>
        /// Confirms a user's email address.
        /// </summary>
        [HttpGet("confirm-email")] // Changed to GET as it's often a link click
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> ConfirmEmail([FromQuery] string email, [FromQuery] string token)
        {
            // IAuthService has ConfirmEmailAsync(string email, string token)
            // The AuthService implementation was changed to ConfirmEmailAsync(string userId, string token)
            // Let's assume the interface and service now align on (string userId, string token) for this endpoint.
            // If IAuthService is strictly (string email, string token), then this controller or the service needs adjustment.
            // For now, proceeding with (userId, token) as per my AuthService implementation.
            // **Correction**: The prompt's IAuthService is `ConfirmEmailAsync(string email, string token)`.
            // My AuthService implementation was `ConfirmEmailAsync(string userId, string token)`.
            // I will assume `AuthService` was corrected to match the interface: `ConfirmEmailAsync(string email, string token)`.
            // So, the controller should take `email` and `token`. The existing controller already does.

            if (string.IsNullOrWhiteSpace(email) || string.IsNullOrWhiteSpace(token))
            {
                 _logger.LogWarning("ConfirmEmail: email or token is missing.");
                return BadRequest(ApiResponse.FailureResponse("Email and token are required for email confirmation."));
            }

            var success = await _authService.ConfirmEmailAsync(email, token);

            if (!success)
            {
                _logger.LogWarning("Email confirmation failed for email associated with token.");
                return BadRequest(ApiResponse.FailureResponse("Email confirmation failed. The link may be invalid or expired."));
            }

            _logger.LogInformation("Email confirmed successfully.");
            // Consider redirecting to a login page or a success page.
            // For an API, returning a success message is fine.
            return Ok(ApiResponse.SuccessResponse(message: "Email confirmed successfully. You can now log in."));
        }

        /// <summary>
        /// Resends the email confirmation link.
        /// </summary>
        [HttpPost("resend-confirmation-email")]
        [AllowAnonymous]
        public async Task<ActionResult<ApiResponse<object>>> ResendConfirmationEmail([FromBody] ResendConfirmationEmailRequestDto request)
        {
            if (string.IsNullOrWhiteSpace(request?.Email))
            {
                return BadRequest(ApiResponse.FailureResponse("Email is required."));
            }

            _logger.LogInformation("Resend confirmation email request for {Email}", request.Email);
            await _authService.ResendEmailConfirmationAsync(request.Email);

            // Always return a positive-sounding message
            return Ok(ApiResponse.SuccessResponse(message: "If an unconfirmed account with that email exists, a new confirmation email has been sent."));
        }

        /// <summary>
        /// Sends a test email to verify SMTP configuration.
        /// </summary>
        [HttpPost("send-test-email")]
        [AllowAnonymous] // For testing purposes - in production you might want to restrict this
        public async Task<ActionResult<ApiResponse<object>>> SendTestEmail([FromBody] TestEmailRequestDto request)
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return BadRequest(ApiResponse<object>.FailureResponse(string.Join(" ", errors)));
            }

            try
            {
                _logger.LogInformation("Test email request to {Email} with subject '{Subject}'", request.ToEmail, request.Subject);

                await _emailService.SendTestEmailAsync(request.ToEmail, request.Subject, request.Message);

                _logger.LogInformation("Test email sent successfully to {Email}", request.ToEmail);
                return Ok(ApiResponse<object>.SuccessResponse(null, $"Test email sent successfully to {request.ToEmail}"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send test email to {Email}", request.ToEmail);
                return StatusCode(500, ApiResponse<object>.FailureResponse($"Failed to send test email: {ex.Message}"));
            }
        }
        
        /// <summary>
        /// Test endpoint.
        /// </summary>
        [HttpGet("test")]
        [AllowAnonymous]
        public ActionResult<ApiResponse<object>> Test()
        {
            _logger.LogInformation("Test endpoint hit.");
            return Ok(ApiResponse.SuccessResponse(new { message = "Hello World from AuthController v1" }, "Test successful"));
        }

        /// <summary>
        /// Checks if a user exists by email. (Primarily for internal/dev use)
        /// </summary>
        [HttpGet("check-user/{email}")]
        [ApiExplorerSettings(IgnoreApi = true)] // Keep ignored unless explicitly needed for frontend checks
        [AllowAnonymous] // Or protected if sensitive
        public async Task<ActionResult<ApiResponse<object>>> CheckUserExists(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                return BadRequest(ApiResponse.FailureResponse("Email cannot be empty."));
            }
            // This still uses _userService. If GetUserByEmailAsync is added to IAuthService, switch to that.
            var user = await _userService.GetUserByEmailAsync(email); // Assuming IUserService has this.
            _logger.LogInformation("Checked user existence for {Email}: {Exists}", email, user != null);
            return Ok(ApiResponse.SuccessResponse(new { exists = user != null }, "User check completed."));
        }
    }

    // Define DTOs if not already globally available or if specific to AuthController actions
    public class LogoutRequestDto
    {
        public string? RefreshToken { get; set; } // Nullable if logout can revoke all tokens for authenticated user
    }

    public class ResendConfirmationEmailRequestDto
    {
        public string Email { get; set; }
    }

    public class TestEmailRequestDto
    {
        [Required(ErrorMessage = "Email address is required.")]
        [EmailAddress(ErrorMessage = "Invalid email format.")]
        public string ToEmail { get; set; } = string.Empty;

        [Required(ErrorMessage = "Subject is required.")]
        [StringLength(200, ErrorMessage = "Subject cannot exceed 200 characters.")]
        public string Subject { get; set; } = string.Empty;

        [Required(ErrorMessage = "Message is required.")]
        [StringLength(5000, ErrorMessage = "Message cannot exceed 5000 characters.")]
        public string Message { get; set; } = string.Empty;
    }
}