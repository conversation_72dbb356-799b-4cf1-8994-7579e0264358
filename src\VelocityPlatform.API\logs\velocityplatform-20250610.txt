[2025-06-10 01:53:47.979 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 01:53:49.559 +01:00 ERR] An error occurred while migrating the database <s:>
System.ArgumentException: Host can't be null
   at Npgsql.NpgsqlConnectionStringBuilder.PostProcessAndValidate()
   at Npgsql.NpgsqlConnection.SetupDataSource()
   at Npgsql.NpgsqlConnection.set_ConnectionString(String value)
   at Npgsql.NpgsqlConnection..ctor(String connectionString)
   at Npgsql.NpgsqlConnection.CloneWith(String connectionString)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlRelationalConnection.CloneWith(String connectionString)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists(Boolean async, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlDatabaseCreator.Exists()
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.Exists()
   at Microsoft.EntityFrameworkCore.Migrations.HistoryRepository.GetAppliedMigrations()
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 321
[2025-06-10 01:53:49.732 +01:00 ERR] An error occurred using the connection to database '' on server ''. <s:Microsoft.EntityFrameworkCore.Database.Connection>
[2025-06-10 01:53:49.743 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Npgsql.ThrowHelper.ThrowInvalidOperationException(String message)
   at Npgsql.NpgsqlConnection.Open(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext() <s:Microsoft.EntityFrameworkCore.Query>
System.InvalidOperationException: The ConnectionString property has not been initialized.
   at Npgsql.ThrowHelper.ThrowInvalidOperationException(String message)
   at Npgsql.NpgsqlConnection.Open(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlConnection.Open()
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenDbConnection(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.OpenInternal(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalConnection.Open(Boolean errorsExpected)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReader(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.InitializeReader(Enumerator enumerator)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.<>c.<MoveNext>b__21_0(DbContext _, Enumerator enumerator)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.Execute[TState,TResult](TState state, Func`3 operation, Func`3 verifySucceeded)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.Enumerator.MoveNext()
[2025-06-10 01:55:08.053 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 01:55:09.970 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 01:55:10.118 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 01:55:10.226 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 01:55:10.229 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 01:55:10.230 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 01:55:10.231 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 01:57:34.685 +01:00 ERR] An unhandled exception occurred: Unable to resolve service for type 'VelocityPlatform.Business.Services.IEnhancedAddonBuilderService' while attempting to activate 'VelocityPlatform.API.Controllers.AddonBuilderController'. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: Unable to resolve service for type 'VelocityPlatform.Business.Services.IEnhancedAddonBuilderService' while attempting to activate 'VelocityPlatform.API.Controllers.AddonBuilderController'.
   at Microsoft.Extensions.DependencyInjection.ActivatorUtilities.ThrowHelperUnableToResolveService(Type type, Type requiredBy)
   at lambda_method53(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 293
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 02:03:48.824 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:03:50.595 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 02:03:50.743 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:03:50.810 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:03:50.813 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:03:50.814 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:03:50.816 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:06:16.560 +01:00 ERR] An unhandled exception occurred: Unable to resolve service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' while attempting to activate 'VelocityPlatform.Business.Services.UserService'. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: Unable to resolve service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' while attempting to activate 'VelocityPlatform.Business.Services.UserService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method59(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 292
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 02:07:15.230 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:07:17.074 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 02:07:17.234 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:07:17.309 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:07:17.311 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:07:17.313 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:07:17.314 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:08:18.141 +01:00 ERR] Error creating component <s:VelocityPlatform.API.Controllers.ComponentsController>
System.InvalidOperationException: User ID not found or invalid
   at VelocityPlatform.API.Controllers.BaseController.GetCurrentUserId() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\BaseController.cs:line 63
   at VelocityPlatform.API.Controllers.ComponentsController.CreateComponent(CreateComponentDto dto) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\ComponentsController.cs:line 97
[2025-06-10 02:08:18.240 +01:00 ERR] An unhandled exception occurred: Unable to resolve service for type 'AutoMapper.IMapper' while attempting to activate 'VelocityPlatform.Business.Services.SiteService'. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: Unable to resolve service for type 'AutoMapper.IMapper' while attempting to activate 'VelocityPlatform.Business.Services.SiteService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method118(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 292
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 02:11:55.142 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:11:57.042 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 02:11:57.194 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:11:57.265 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:11:57.268 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:11:57.269 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:11:57.270 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:13:47.686 +01:00 ERR] An unhandled exception occurred: Unable to resolve service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' while attempting to activate 'VelocityPlatform.Business.Services.UserService'. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: Unable to resolve service for type 'Microsoft.AspNetCore.Identity.RoleManager`1[Microsoft.AspNetCore.Identity.IdentityRole]' while attempting to activate 'VelocityPlatform.Business.Services.UserService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method53(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 292
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 02:14:48.542 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:14:50.744 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 02:14:50.896 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:14:50.965 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:14:50.968 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:14:50.970 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:14:50.971 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:15:26.485 +01:00 ERR] An unhandled exception occurred: Unable to resolve service for type 'AutoMapper.IMapper' while attempting to activate 'VelocityPlatform.Business.Services.UserService'. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.InvalidOperationException: Unable to resolve service for type 'AutoMapper.IMapper' while attempting to activate 'VelocityPlatform.Business.Services.UserService'.
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateArgumentCallSites(ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain, ParameterInfo[] parameters, Boolean throwIfCallSiteNotFound)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateConstructorCallSite(ResultCache lifetime, ServiceIdentifier serviceIdentifier, Type implementationType, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceDescriptor descriptor, ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain, Int32 slot)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.TryCreateExact(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.CallSiteFactory.CreateCallSite(ServiceIdentifier serviceIdentifier, CallSiteChain callSiteChain)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.CreateServiceAccessor(ServiceIdentifier serviceIdentifier)
   at System.Collections.Concurrent.ConcurrentDictionary`2.GetOrAdd(TKey key, Func`2 valueFactory)
   at Microsoft.Extensions.DependencyInjection.ServiceProvider.GetService(ServiceIdentifier serviceIdentifier, ServiceProviderEngineScope serviceProviderEngineScope)
   at Microsoft.Extensions.DependencyInjection.ServiceLookup.ServiceProviderEngineScope.GetService(Type serviceType)
   at lambda_method53(Closure, IServiceProvider, Object[])
   at Microsoft.AspNetCore.Mvc.Controllers.ControllerFactoryProvider.<>c__DisplayClass6_0.<CreateControllerFactory>g__CreateController|0(ControllerContext controllerContext)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 292
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 02:18:11.737 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:18:13.556 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 02:18:13.741 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:18:13.810 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:18:13.813 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:18:13.814 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:18:13.816 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:18:52.023 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 02:20:40.259 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 02:20:40.263 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 02:20:40.370 +01:00 ERR] Failed executing DbCommand (34ms) [Parameters=[@__normalizedEmail_0='?'], CommandType='"Text"', CommandTimeout='30']
SELECT a."Id", a."AccessFailedCount", a."AnonymizedDate", a."ApiKey", a."AvatarUrl", a."ConcurrencyStamp", a."ConfirmationToken", a."ConfirmationTokenExpiry", a."CreatedAt", a."Email", a."EmailConfirmed", a."EmailVerified", a."FailedLoginAttempts", a."FirstName", a."IsActive", a."IsAnonymized", a."IsLockedOut", a."LastLoginAt", a."LastName", a."LastPasswordChange", a."LockedUntil", a."LockoutEnabled", a."LockoutEnd", a."NormalizedEmail", a."NormalizedUserName", a."PasswordHash", a."PasswordSalt", a."PhoneNumber", a."PhoneNumberConfirmed", a."PreferencesJson", a."ProfileData", a."ResetToken", a."ResetTokenExpiry", a."Role", a."SecurityStamp", a."TenantId", a."TwoFactorEnabled", a."UpdatedAt", a."UserName"
FROM "AspNetUsers" AS a
WHERE a."NormalizedEmail" = @__normalizedEmail_0
LIMIT 2 <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 02:20:40.388 +01:00 ERR] An exception occurred while iterating over the results of a query for context type 'VelocityPlatform.Data.VelocityPlatformDbContext'.
Npgsql.PostgresException (0x80004005): 42703: column a.AvatarUrl does not exist

POSITION: 71
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.AvatarUrl does not exist
    Position: 71
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn <s:Microsoft.EntityFrameworkCore.Query>
Npgsql.PostgresException (0x80004005): 42703: column a.AvatarUrl does not exist

POSITION: 71
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.AvatarUrl does not exist
    Position: 71
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 02:20:40.406 +01:00 ERR] An unhandled exception occurred: 42703: column a.AvatarUrl does not exist

POSITION: 71 <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
Npgsql.PostgresException (0x80004005): 42703: column a.AvatarUrl does not exist

POSITION: 71
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteDbDataReaderAsync(CommandBehavior behavior, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteReaderAsync(RelationalCommandParameterObject parameterObject, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.InitializeReaderAsync(AsyncEnumerator enumerator, CancellationToken cancellationToken)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Storage.Internal.NpgsqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.Internal.SingleQueryingEnumerable`1.AsyncEnumerator.MoveNextAsync()
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Query.ShapedQueryCompilingExpressionVisitor.SingleOrDefaultAsync[TSource](IAsyncEnumerable`1 asyncEnumerable, CancellationToken cancellationToken)
   at Microsoft.AspNetCore.Identity.UserManager`1.FindByEmailAsync(String email)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 47
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 116
   at lambda_method57(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
  Exception data:
    Severity: ERROR
    SqlState: 42703
    MessageText: column a.AvatarUrl does not exist
    Position: 71
    File: parse_relation.c
    Line: 3721
    Routine: errorMissingColumn
[2025-06-10 02:27:50.363 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:27:53.655 +01:00 ERR] Failed executing DbCommand (25ms) [Parameters=[], CommandType='"Text"', CommandTimeout='30']
ALTER TABLE "IsolationPolicies" ALTER COLUMN "Id" TYPE uuid;
ALTER TABLE "IsolationPolicies" ALTER COLUMN "Id" DROP IDENTITY; <s:Microsoft.EntityFrameworkCore.Database.Command>
[2025-06-10 02:27:53.682 +01:00 ERR] An error occurred while migrating the database <s:>
Npgsql.PostgresException (0x80004005): 22023: identity column type must be smallint, integer, or bigint
   at Npgsql.Internal.NpgsqlConnector.ReadMessageLong(Boolean async, DataRowLoadingMode dataRowLoadingMode, Boolean readingNotifications, Boolean isReadingPrependedMessage)
   at System.Runtime.CompilerServices.PoolingAsyncValueTaskMethodBuilder`1.StateMachineBox`1.System.Threading.Tasks.Sources.IValueTaskSource<TResult>.GetResult(Int16 token)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult(Boolean async, Boolean isConsuming, CancellationToken cancellationToken)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteReader(Boolean async, CommandBehavior behavior, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery(Boolean async, CancellationToken cancellationToken)
   at Npgsql.NpgsqlCommand.ExecuteNonQuery()
   at Microsoft.EntityFrameworkCore.Storage.RelationalCommand.ExecuteNonQuery(RelationalCommandParameterObject parameterObject)
   at Microsoft.EntityFrameworkCore.Migrations.MigrationCommand.ExecuteNonQuery(IRelationalConnection connection, IReadOnlyDictionary`2 parameterValues)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationCommandExecutor.ExecuteNonQuery(IEnumerable`1 migrationCommands, IRelationalConnection connection)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.Migrate(String targetMigration)
   at Npgsql.EntityFrameworkCore.PostgreSQL.Migrations.Internal.NpgsqlMigrator.Migrate(String targetMigration)
   at Microsoft.EntityFrameworkCore.RelationalDatabaseFacadeExtensions.Migrate(DatabaseFacade databaseFacade)
   at Program.<Main>$(String[] args) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 323
  Exception data:
    Severity: ERROR
    SqlState: 22023
    MessageText: identity column type must be smallint, integer, or bigint
    File: sequence.c
    Line: 1384
    Routine: init_params
[2025-06-10 02:27:53.884 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:27:53.950 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:27:53.952 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:27:53.954 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:27:53.955 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:28:24.705 +01:00 INF] Application is shutting down... <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:38:36.109 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 02:38:38.210 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 02:38:38.363 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 02:38:38.438 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:38:38.440 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:38:38.442 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:38:38.443 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 02:39:51.870 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 02:40:20.352 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 02:40:20.357 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 02:40:20.553 +01:00 ERR] An unhandled exception occurred: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at Microsoft.AspNetCore.Identity.PasswordHasher`1.VerifyHashedPassword(TUser user, String hashedPassword, String providedPassword)
   at Microsoft.AspNetCore.Identity.UserManager`1.VerifyPasswordAsync(IUserPasswordStore`1 store, TUser user, String password)
   at Microsoft.AspNetCore.Identity.UserManager`1.CheckPasswordAsync(TUser user, String password)
   at Microsoft.AspNetCore.Identity.SignInManager`1.CheckPasswordSignInAsync(TUser user, String password, Boolean lockoutOnFailure)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 61
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 116
   at lambda_method57(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 02:40:35.052 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 02:40:35.055 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 02:40:35.118 +01:00 ERR] An unhandled exception occurred: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.FormatException: The input is not a valid Base-64 string as it contains a non-base 64 character, more than two padding characters, or an illegal character among the padding characters.
   at System.Convert.FromBase64CharPtr(Char* inputPtr, Int32 inputLength)
   at System.Convert.FromBase64String(String s)
   at Microsoft.AspNetCore.Identity.PasswordHasher`1.VerifyHashedPassword(TUser user, String hashedPassword, String providedPassword)
   at Microsoft.AspNetCore.Identity.UserManager`1.VerifyPasswordAsync(IUserPasswordStore`1 store, TUser user, String password)
   at Microsoft.AspNetCore.Identity.UserManager`1.CheckPasswordAsync(TUser user, String password)
   at Microsoft.AspNetCore.Identity.SignInManager`1.CheckPasswordSignInAsync(TUser user, String password, Boolean lockoutOnFailure)
   at VelocityPlatform.Business.Services.AuthService.LoginAsync(LoginRequestDto request, String ipAddress, String userAgent) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 61
   at VelocityPlatform.API.Controllers.AuthController.Login(LoginRequestDto request) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 116
   at lambda_method57(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeInnerFilterAsync>g__Awaited|13_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at Microsoft.AspNetCore.RateLimiting.RateLimitingMiddleware.InvokeInternal(HttpContext context, EnableRateLimitingAttribute enableRateLimitingAttribute)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 16:48:32.939 +01:00 INF] Application is shutting down... <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:14:59.844 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 17:15:01.759 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 17:15:01.916 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 17:15:02.003 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:15:02.006 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:15:02.008 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:15:02.009 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 17:15:41.752 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 17:15:41.758 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:15:42.044 +01:00 INF] Login successful <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 17:15:42.047 +01:00 INF] Login <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:38:38.676 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:38:40.536 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:38:40.685 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:38:40.751 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:38:40.754 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:38:40.755 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:38:40.756 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:39:54.363 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:39:54.456 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:39:54.460 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:39:54.916 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:39:54.920 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:39:54.935 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:39:54.952 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:39:54.955 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:39:55.160 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: false, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:39:55.163 +01:00 WRN] Login <NAME_EMAIL>: Invalid email or password. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:39:55.165 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:41:03.835 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:03.842 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:03.845 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.052 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.057 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for Test. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUY31pVsFf6kacbhHFXwYRg9KgnHhLpY0vdhN7dBMb64KzNTGhyrWo+dPaIB9lpD5gXoFt+dugFX2QfLs1fvYw6Uxuze+sAYBXxCb4jRIfbM2bJQKTimCoUVfJwwaWtq5yZaDHscOKSpqyMzOfL2PdOP243aBWu3qEXKtHkoY/YbP2zC8XR7pHzEAZ6pcA/GhAtzBFSQdBrDKeAL7aatv5fQXShA973Wy97Hpt+GTMTKFw== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-10 23:41:04.167 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.194 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.197 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.220 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.222 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.226 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.228 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.250 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.252 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.257 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.259 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.409 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.412 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.415 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:41:04.423 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:41:04.426 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.448 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:41:04.450 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:23.996 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.001 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.003 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.026 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.028 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.044 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.046 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.069 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.071 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.076 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.078 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.101 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.103 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.108 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.110 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.132 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.134 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.137 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:42:24.142 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:42:24.144 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.311 +01:00 INF] User <EMAIL> registered successfully. Sending confirmation email. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:42:24.313 +01:00 INF] Mock Email: Email confirmation <NAME_EMAIL> for New. Token: CfDJ8EjbdKbUnLBGjTf5mNF9tUYJ1BN+P0MkMKFEBvgwlp+fS77q1iNSK4daHQ+HN6IFwUxpdTqCD5cIiqjcl90KV2gNaP+qog3tljx++XxP6XbcvBEL+KfSvMO4UtsDOAojHftpISxUV6MRY+dmoTcj7EsqvEecP5KMF5FW6IlosOR2OCML6NsNnFRX3qrsONX/zbL+0PTrmj0Sn2EZN04OtOrOwW5XLGDG9DCIvX54OHr5kk8GyCwN+FhL4Rw07Nmjhg== <s:VelocityPlatform.Business.Services.MockEmailService>
[2025-06-10 23:42:24.418 +01:00 INF] Registration <NAME_EMAIL>. Please check email for confirmation. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.495 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.501 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.502 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:15.524 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:15.528 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.536 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.538 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:15.559 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:15.562 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.566 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:15.568 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:15.590 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:15.592 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:24.050 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:24.053 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:24.074 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:24.077 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:24.079 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:43:24.084 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:24.086 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:24.107 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:43:24.109 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:43:55.618 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:43:57.751 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:43:57.905 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:43:57.979 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:43:57.982 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:43:57.984 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:43:57.986 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:44:17.283 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.397 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.402 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.589 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.593 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.631 +01:00 INF] Confirm email attempt for User ID: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.634 +01:00 ERR] An unhandled exception occurred: Unrecognized Guid format. <s:VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware>
System.FormatException: Unrecognized Guid format.
   at System.Guid.GuidResult.SetFailure(ParseFailure failureKind)
   at System.Guid.TryParseGuid(ReadOnlySpan`1 guidString, GuidResult& result)
   at System.Guid..ctor(String g)
   at System.ComponentModel.GuidConverter.ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, Object value)
   at Microsoft.AspNetCore.Identity.UserStoreBase`5.ConvertIdFromString(String id)
   at Microsoft.AspNetCore.Identity.EntityFrameworkCore.UserStore`9.FindByIdAsync(String userId, CancellationToken cancellationToken)
   at VelocityPlatform.Business.Services.AuthService.ConfirmEmailAsync(String userId, String token) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.Business\Services\AuthService.cs:line 306
   at VelocityPlatform.API.Controllers.AuthController.ConfirmEmail(String email, String token) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Controllers\AuthController.cs:line 345
   at lambda_method340(Closure, Object)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ActionMethodExecutor.AwaitableObjectResultExecutor.Execute(ActionContext actionContext, IActionResultTypeMapper mapper, ObjectMethodExecutor executor, Object controller, Object[] arguments)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeActionMethodAsync>g__Awaited|12_0(ControllerActionInvoker invoker, ValueTask`1 actionResultValueTask)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.<InvokeNextActionFilterAsync>g__Awaited|10_0(ControllerActionInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Rethrow(ActionExecutedContextSealed context)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.Next(State& next, Scope& scope, Object& state, Boolean& isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ControllerActionInvoker.InvokeInnerFilterAsync()
--- End of stack trace from previous location ---
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeFilterPipelineAsync>g__Awaited|20_0(ResourceInvoker invoker, Task lastTask, State next, Scope scope, Object state, Boolean isCompleted)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at Microsoft.AspNetCore.Mvc.Infrastructure.ResourceInvoker.<InvokeAsync>g__Awaited|17_0(ResourceInvoker invoker, Task task, IDisposable scope)
   at VelocityPlatform.API.Middleware.TenantMiddleware.InvokeAsync(HttpContext context, ITenantProvider tenantProvider) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\TenantMiddleware.cs:line 21
   at VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\AuthorizationLoggingMiddleware.cs:line 21
   at Microsoft.AspNetCore.Authorization.AuthorizationMiddleware.Invoke(HttpContext context)
   at Microsoft.AspNetCore.Authentication.AuthenticationMiddleware.Invoke(HttpContext context)
   at Program.<>c.<<<Main>$>b__0_12>d.MoveNext() in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Program.cs:line 295
--- End of stack trace from previous location ---
   at Swashbuckle.AspNetCore.SwaggerUI.SwaggerUIMiddleware.Invoke(HttpContext httpContext)
   at Swashbuckle.AspNetCore.Swagger.SwaggerMiddleware.Invoke(HttpContext httpContext, ISwaggerProvider swaggerProvider)
   at VelocityPlatform.API.Middleware.GlobalExceptionHandlerMiddleware.InvokeAsync(HttpContext context) in C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API\Middleware\GlobalExceptionHandlerMiddleware.cs:line 27
[2025-06-10 23:44:17.670 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.673 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.812 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.814 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.820 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.823 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.846 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.848 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.853 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.856 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.882 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.886 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.889 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:44:17.895 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:44:17.897 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.919 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:44:17.921 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:15.403 +01:00 INF] Environment: Production <s:VelocityPlatform.API>
[2025-06-10 23:45:17.809 +01:00 INF] Database migration completed successfully <s:>
[2025-06-10 23:45:18.075 +01:00 INF] Velocity Platform API starting up... <s:>
[2025-06-10 23:45:18.160 +01:00 INF] Now listening on: http://localhost:5000 <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:45:18.163 +01:00 INF] Application started. Press Ctrl+C to shut down. <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:45:18.164 +01:00 INF] Hosting environment: Production <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:45:18.165 +01:00 INF] Content root path: C:\Users\<USER>\Desktop\My Ideas\VWPlatformweb\src\VelocityPlatform.API <s:Microsoft.Hosting.Lifetime>
[2025-06-10 23:45:37.791 +01:00 INF] Test endpoint hit. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:37.907 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:37.911 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.119 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.121 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.156 +01:00 INF] Confirm email attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.245 +01:00 ERR] Email confirmation <NAME_EMAIL>: Invalid token. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.248 +01:00 WRN] Email confirmation failed for email associated with token. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.265 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.270 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.377 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.380 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.385 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.388 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.411 +01:00 WRN] Login failed: Email not confirmed <NAME_EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.413 +01:00 WRN] Login <NAME_EMAIL>: Email not confirmed. Please check your inbox. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.418 +01:00 INF] Login <NAME_EMAIL> from IP ::1 <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.420 +01:00 INF] Login attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.447 +01:00 WRN] Login failed: Invalid password <NAME_EMAIL>. Lockout: true, NotAllowed: false <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.450 +01:00 WRN] Login <NAME_EMAIL>: Account locked due to too many failed login attempts. <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.454 +01:00 WRN] Authorization Failure: User Anonymous attempted POST /api/v1/Auth/login from IP ::1 and received 401. <s:VelocityPlatform.API.Middleware.AuthorizationLoggingMiddleware>
[2025-06-10 23:45:38.460 +01:00 INF] Registration <NAME_EMAIL> <s:VelocityPlatform.API.Controllers.AuthController>
[2025-06-10 23:45:38.462 +01:00 INF] Registration attempt for email: <EMAIL> <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.485 +01:00 WRN] Registration failed: Email <EMAIL> already exists. <s:VelocityPlatform.Business.Services.AuthService>
[2025-06-10 23:45:38.487 +01:00 WRN] Registration <NAME_EMAIL>: Email already registered. <s:VelocityPlatform.API.Controllers.AuthController>
