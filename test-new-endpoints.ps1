# VelocityPlatform API - New Endpoints Testing Script
Write-Host "🧪 Testing NEW VelocityPlatform API Endpoints" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

$baseUrl = "http://localhost:5000"
$headers = @{ "Content-Type" = "application/json" }

# Test 1: Component Templates (NEW)
Write-Host "`n1. Testing Component Templates..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/templates" -Method Get -TimeoutSec 10
    Write-Host "✅ Component templates working" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) templates" -ForegroundColor Cyan
    if ($response.data.Count -gt 0) {
        Write-Host "Sample template: $($response.data[0].name)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Component templates failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: Component Categories (NEW)
Write-Host "`n2. Testing Component Categories..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/categories" -Method Get -TimeoutSec 10
    Write-Host "✅ Component categories working" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) categories" -ForegroundColor Cyan
    if ($response.data.Count -gt 0) {
        Write-Host "Sample category: $($response.data[0].displayName)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Component categories failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 3: Get Components (NEW)
Write-Host "`n3. Testing Get Components..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components?pageSize=5" -Method Get -TimeoutSec 10
    Write-Host "✅ Get components working" -ForegroundColor Green
    Write-Host "Found $($response.data.data.Count) components (page 1)" -ForegroundColor Cyan
    Write-Host "Total components: $($response.data.totalCount)" -ForegroundColor Cyan
    if ($response.data.data.Count -gt 0) {
        Write-Host "Sample component: $($response.data.data[0].name)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Get components failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 4: File Categories (NEW)
Write-Host "`n4. Testing File Categories..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Files/categories" -Method Get -TimeoutSec 10
    Write-Host "✅ File categories working" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) categories" -ForegroundColor Cyan
    Write-Host "Categories: $($response.data -join ', ')" -ForegroundColor Cyan
} catch {
    Write-Host "❌ File categories failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Media Files (NEW)
Write-Host "`n5. Testing Media Files..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Files/media?pageSize=5" -Method Get -TimeoutSec 10
    Write-Host "✅ Media files working" -ForegroundColor Green
    Write-Host "Found $($response.data.data.Count) media files (page 1)" -ForegroundColor Cyan
    Write-Host "Total media files: $($response.data.totalCount)" -ForegroundColor Cyan
    if ($response.data.data.Count -gt 0) {
        Write-Host "Sample file: $($response.data.data[0].fileName)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Media files failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Addon Builder Templates (EXISTING - should work)
Write-Host "`n6. Testing Addon Builder Templates..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/addon-builder/templates" -Method Get -TimeoutSec 10
    Write-Host "✅ Addon builder templates working" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) addon templates" -ForegroundColor Cyan
    if ($response.data.Count -gt 0) {
        Write-Host "Sample template: $($response.data[0].name)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Addon builder templates failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 7: Create Component (NEW)
Write-Host "`n7. Testing Create Component..." -ForegroundColor Yellow
try {
    $componentData = @{
        name = "Test Component $(Get-Random)"
        description = "A test component created via API"
        category = "test"
        tags = @("test", "api")
        htmlTemplate = "<div class='test-component'>Test Content</div>"
        cssStyles = ".test-component { color: blue; }"
        javascriptCode = "console.log('Test component loaded');"
        isGlobal = $false
    } | ConvertTo-Json

    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components" -Method Post -Body $componentData -Headers $headers -TimeoutSec 10
    Write-Host "✅ Create component working" -ForegroundColor Green
    Write-Host "Created component: $($response.data.name)" -ForegroundColor Cyan
    Write-Host "Component ID: $($response.data.id)" -ForegroundColor Cyan
    
    # Store component ID for further tests
    $global:testComponentId = $response.data.id
} catch {
    Write-Host "❌ Create component failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 8: Get Specific Component (NEW)
Write-Host "`n8. Testing Get Specific Component..." -ForegroundColor Yellow
try {
    if ($global:testComponentId) {
        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/$global:testComponentId" -Method Get -TimeoutSec 10
        Write-Host "✅ Get specific component working" -ForegroundColor Green
        Write-Host "Retrieved component: $($response.data.name)" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no test component ID available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Get specific component failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 9: Component Preview (NEW)
Write-Host "`n9. Testing Component Preview..." -ForegroundColor Yellow
try {
    if ($global:testComponentId) {
        $previewData = @{
            configuration = @{ title = "Preview Test" }
            sampleData = @{ content = "Sample content" }
            theme = "default"
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/$global:testComponentId/preview" -Method Post -Body $previewData -Headers $headers -TimeoutSec 10
        Write-Host "✅ Component preview working" -ForegroundColor Green
        Write-Host "Preview generated successfully: $($response.data.success)" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no test component ID available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Component preview failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 10: Component Validation (NEW)
Write-Host "`n10. Testing Component Validation..." -ForegroundColor Yellow
try {
    if ($global:testComponentId) {
        $validationData = @{
            configuration = @{ title = "Validation Test" }
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Components/$global:testComponentId/validate" -Method Post -Body $validationData -Headers $headers -TimeoutSec 10
        Write-Host "✅ Component validation working" -ForegroundColor Green
        Write-Host "Validation result: $($response.data.isValid)" -ForegroundColor Cyan
        Write-Host "Performance score: $($response.data.performanceMetrics.complexityScore)" -ForegroundColor Cyan
    } else {
        Write-Host "⚠️ Skipping - no test component ID available" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Component validation failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 11: Enhanced Addon Builder - Marketplace Categories (NEW)
Write-Host "`n11. Testing Addon Marketplace Categories..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/addon-builder/marketplace/categories" -Method Get -TimeoutSec 10
    Write-Host "✅ Addon marketplace categories working" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) marketplace categories" -ForegroundColor Cyan
    if ($response.data.Count -gt 0) {
        Write-Host "Sample category: $($response.data[0].displayName)" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Addon marketplace categories failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 12: Website Builder - Layout Templates (NEW)
Write-Host "`n12. Testing Layout Templates..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/v1/Sites/layout-templates" -Method Get -TimeoutSec 10
    Write-Host "✅ Layout templates working" -ForegroundColor Green
    Write-Host "Found $($response.data.Count) layout templates" -ForegroundColor Cyan
} catch {
    Write-Host "❌ Layout templates failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 NEW Endpoint testing completed!" -ForegroundColor Green
Write-Host "Summary of NEW features tested:" -ForegroundColor Cyan
Write-Host "- ✅ File Management System (Categories, Media Files)" -ForegroundColor Cyan
Write-Host "- ✅ Component Management System (CRUD, Templates, Categories)" -ForegroundColor Cyan
Write-Host "- ✅ Enhanced Addon Builder (Marketplace Categories)" -ForegroundColor Cyan
Write-Host "- ✅ Website Builder API (Layout Templates)" -ForegroundColor Cyan
