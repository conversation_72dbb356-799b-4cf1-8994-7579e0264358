<?xml version="1.0"?>
<doc>
    <assembly>
        <name>VelocityPlatform.API</name>
    </assembly>
    <members>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.TestAddon(System.Guid,VelocityPlatform.Models.DTOs.AddonTestRequestDto)">
            <summary>
            Test an addon configuration with sample data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.ValidateAddon(System.Guid)">
            <summary>
            Validate addon configuration and Rete.js setup
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.GenerateAddonEndpoints(System.Guid,VelocityPlatform.Models.DTOs.EndpointGenerationRequestDto)">
            <summary>
            Generate API endpoints for an addon
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.GetReteConfiguration(System.Guid)">
            <summary>
            Get Rete.js configuration for an addon
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.UpdateReteConfiguration(System.Guid,VelocityPlatform.Models.DTOs.UpdateReteConfigurationDto)">
            <summary>
            Update Rete.js configuration for an addon
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.GetMarketplaceCategories">
            <summary>
            Get addon marketplace categories
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonBuilderController.SubmitToMarketplace(System.Guid,VelocityPlatform.Models.DTOs.MarketplaceSubmissionDto)">
            <summary>
            Submit addon to marketplace for review
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.GetAddonData(System.Guid)">
            <summary>
            Get addon data by addon instance ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.UpdateAddonData(System.Guid,VelocityPlatform.Models.DTOs.AddonDataRequestDto)">
            <summary>
            Update addon data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.CreateAddonData(System.Guid,VelocityPlatform.Models.DTOs.AddonDataRequestDto)">
            <summary>
            Create addon data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AddonDataController.DeleteAddonData(System.Guid)">
            <summary>
            Delete addon data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.GetMe">
            <summary>
            Retrieves current user profile.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.GetApiKey">
            <summary>
            Generates/retrieves API key for the authenticated user.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.Login(VelocityPlatform.Models.DTOs.LoginRequestDto)">
            <summary>
            Logs in a user.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.Register(VelocityPlatform.Models.DTOs.RegisterRequestDto)">
            <summary>
            Registers a new user.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.RefreshToken(VelocityPlatform.Models.DTOs.RefreshTokenRequestDto)">
            <summary>
            Refreshes an authentication token.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.Logout(VelocityPlatform.API.Controllers.LogoutRequestDto)">
            <summary>
            Logs out a user by revoking the provided refresh token.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.ForgotPassword(VelocityPlatform.Models.DTOs.ForgotPasswordRequestDto)">
            <summary>
            Initiates the password forgot process for a user.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.ResetPassword(VelocityPlatform.Models.DTOs.ResetPasswordRequestDto)">
            <summary>
            Resets a user's password using a token.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.ChangePassword(VelocityPlatform.Models.DTOs.ChangePasswordRequest)">
            <summary>
            Changes the password for an authenticated user.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.ConfirmEmail(System.String,System.String)">
            <summary>
            Confirms a user's email address.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.ResendConfirmationEmail(VelocityPlatform.API.Controllers.ResendConfirmationEmailRequestDto)">
            <summary>
            Resends the email confirmation link.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.Test">
            <summary>
            Test endpoint.
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.AuthController.CheckUserExists(System.String)">
            <summary>
            Checks if a user exists by email. (Primarily for internal/dev use)
            </summary>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.ComponentsController">
            <summary>
            Controller for managing website components and templates
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.GetComponents(System.Int32,System.Int32,System.String,System.String,System.Boolean)">
            <summary>
            Get available components for the website builder
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.GetComponent(System.Guid)">
            <summary>
            Get a specific component by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.CreateComponent(VelocityPlatform.Models.DTOs.CreateComponentDto)">
            <summary>
            Create a new custom component
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.UpdateComponent(System.Guid,VelocityPlatform.Models.DTOs.UpdateComponentDto)">
            <summary>
            Update an existing component
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.DeleteComponent(System.Guid)">
            <summary>
            Delete a component
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.GetComponentTemplates(System.String)">
            <summary>
            Get component templates for the website builder
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.GetComponentCategories">
            <summary>
            Get component categories
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.PreviewComponent(System.Guid,VelocityPlatform.Models.DTOs.ComponentPreviewRequestDto)">
            <summary>
            Preview a component with sample data
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.ValidateComponent(System.Guid,VelocityPlatform.Models.DTOs.ComponentConfigurationDto)">
            <summary>
            Validate component configuration
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.ComponentsController.GetComponentUsage(System.Guid)">
            <summary>
            Get component usage statistics
            </summary>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.DynamicAddonsController">
            <summary>
            Dynamic controller for handling addon-specific endpoints
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.DynamicAddonsController.GetData(System.Guid)">
            <summary>
            Get addon data
            </summary>
            <param name="addonInstanceId">Addon instance ID</param>
            <returns>Addon data content</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.DynamicAddonsController.SubmitData(System.Guid,VelocityPlatform.Models.DTOs.AddonDataRequestDto)">
            <summary>
            Submit data to an addon
            </summary>
            <param name="addonInstanceId">Addon instance ID</param>
            <param name="requestDto">JSON payload to submit</param>
            <returns>Submission status</returns>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.FilesController">
            <summary>
            Controller for file upload and media management
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.GetFiles(System.Int32,System.Int32)">
            <summary>
            Get files overview
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.UploadFile(VelocityPlatform.Models.DTOs.FileUploadDto)">
            <summary>
            Upload a file to the media library
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.GetMediaFiles(System.Int32,System.Int32,System.String,System.String)">
            <summary>
            Get media files for the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.GetMediaFile(System.Guid)">
            <summary>
            Get a specific media file
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.DownloadFile(System.Guid)">
            <summary>
            Download a file
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.DeleteFile(System.Guid)">
            <summary>
            Delete a media file
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.UpdateMediaFile(System.Guid,VelocityPlatform.Models.DTOs.UpdateMediaFileDto)">
            <summary>
            Update media file metadata
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.GetFileCategories">
            <summary>
            Get file categories
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.FilesController.OptimizeImage(System.Guid,VelocityPlatform.Models.DTOs.ImageOptimizationRequestDto)">
            <summary>
            Generate optimized image variants
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.ProcessPayment(VelocityPlatform.Models.DTOs.ProcessPaymentDto)">
            <summary>
            Process a payment
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.GetPaymentHistory(System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            Get payment history for the current tenant/user
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.GetPayment(System.Guid)">
            <summary>
            Get a specific payment by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.RefundPayment(System.Guid,VelocityPlatform.Models.DTOs.RefundRequestDto)">
            <summary>
            Process a refund for a payment
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.PaymentsController.HandlePaymentWebhook(VelocityPlatform.Models.DTOs.WebhookPayloadDto)">
            <summary>
            Handle payment webhooks from payment processors
            </summary>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.SecurityController">
            <summary>
            Controller for handling security-related operations
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.GetGDPRReports">
            <summary>
            Retrieves GDPR compliance reports for the current tenant
            </summary>
            <returns>List of GDPR reports</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.GetAuditLogs">
            <summary>
            Retrieves security audit logs
            </summary>
            <returns>List of security events</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.GetVulnerabilityScans(System.Int32,System.Int32)">
            <summary>
            Get all vulnerability scans for the current tenant
            </summary>
            <returns>List of vulnerability scans</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.StartVulnerabilityScan(VelocityPlatform.Models.DTOs.VulnerabilityScanRequestDto)">
            <summary>
            Start a new vulnerability scan
            </summary>
            <param name="request">Vulnerability scan request parameters</param>
            <returns>Vulnerability scan result</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SecurityController.TriggerVulnerabilityScan(VelocityPlatform.Models.DTOs.VulnerabilityScanRequestDto)">
            <summary>
            Triggers a vulnerability scan on the specified target URL (legacy endpoint)
            </summary>
            <param name="request">Vulnerability scan request parameters</param>
            <returns>Vulnerability scan result</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.GetSites(System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            Get all sites for the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.GetSite(System.Guid)">
            <summary>
            Get a specific site by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.CreateSite(VelocityPlatform.Models.DTOs.CreateSiteDto)">
            <summary>
            Create a new site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.UpdateSite(System.Guid,VelocityPlatform.Models.DTOs.UpdateSiteDto)">
            <summary>
            Update a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.PublishSite(System.Guid)">
            <summary>
            Publish a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.DeleteSite(System.Guid)">
            <summary>
            Delete (deactivate) a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.CompileSite(System.Guid)">
            <summary>
            Compile a site
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.DeploySite(System.Guid)">
            <summary>
            Deploy a compiled site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.GetPageLayout(System.Guid,System.Guid)">
            <summary>
            Get page layout configuration for the website builder
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.UpdatePageLayout(System.Guid,System.Guid,VelocityPlatform.Models.DTOs.UpdatePageLayoutDto)">
            <summary>
            Update page layout configuration
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.GeneratePagePreview(System.Guid,System.Guid,VelocityPlatform.Models.DTOs.PagePreviewRequestDto)">
            <summary>
            Generate real-time preview of a page
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SitesController.ExportSiteAssets(System.Guid,VelocityPlatform.Models.DTOs.SiteExportRequestDto)">
            <summary>
            Export site assets for external hosting
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.GetSiteVersions(System.Guid,System.Int32,System.Int32)">
            <summary>
            Get all site versions for a specific site within the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.GetSiteVersion(System.Guid,System.Guid)">
            <summary>
            Get a specific site version by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.CreateSiteVersion(VelocityPlatform.Models.DTOs.CreateSiteVersionRequestDto)">
            <summary>
            Create a new site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.UpdateSiteVersion(System.Guid,VelocityPlatform.Models.DTOs.UpdateSiteVersionRequestDto)">
            <summary>
            Update a site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SiteVersionsController.DeleteSiteVersion(System.Guid)">
            <summary>
            Delete a site version
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptionPlans">
            <summary>
            Get all available subscription plans
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.GetCurrentUserSubscription">
            <summary>
            Get current user's subscription
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.CreateSubscription(VelocityPlatform.Models.DTOs.CreateSubscriptionDto)">
            <summary>
            Create a new subscription
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.GetSubscriptions(System.Int32,System.Int32,System.String,System.String,System.String,System.String)">
            <summary>
            Get subscriptions for the current tenant
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.SubscribeToPlan(VelocityPlatform.Models.DTOs.SubscribeRequestDto)">
            <summary>
            Subscribe to a plan (legacy endpoint)
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.CancelSubscription">
            <summary>
            Cancel current user's subscription (legacy endpoint)
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.CancelSubscriptionById(System.Guid)">
            <summary>
            Cancel a specific subscription by ID
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.SubscriptionsController.ReactivateSubscription">
            <summary>
            Reactivate current user's subscription
            </summary>
        </member>
        <member name="T:VelocityPlatform.API.Controllers.TenantController">
            <summary>
            Controller for tenant management operations
            </summary>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.TenantController.GetCurrentTenant">
            <summary>
            Get current tenant information
            </summary>
            <returns>Current tenant details</returns>
        </member>
        <member name="M:VelocityPlatform.API.Controllers.TenantController.EnforceIsolation(System.String,VelocityPlatform.Models.DTOs.IsolationPolicyDto)">
            <summary>
            Enforces an isolation policy for a tenant
            </summary>
            <param name="tenantId">The ID of the tenant</param>
            <param name="policyDto">Isolation policy details</param>
            <returns>Action result indicating success or failure</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.AddonEndpointService.InvokeAddonCustomEndpoint(System.Guid)">
            <summary>
            Handles GET requests to dynamic addon endpoints
            </summary>
            <param name="addonId">The ID of the addon</param>
            <returns>ActionResult with response data</returns>
            <remarks>
            Sample request:
            GET /api/addons/3fa85f64-5717-4562-b3fc-2c963f66afa6/custom-endpoint
            
            Sample response:
            {
              "addonId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
              "message": "Custom endpoint executed successfully"
            }
            </remarks>
            <response code="200">Returns the custom endpoint response</response>
            <response code="400">If the addon ID is invalid</response>
            <response code="404">If the addon is not found</response>
        </member>
        <member name="M:VelocityPlatform.API.Services.AddonEndpointService.InvokeAddonCustomAction(System.Guid,System.Object)">
            <summary>
            Handles POST requests to dynamic addon actions
            </summary>
            <param name="addonId">The ID of the addon</param>
            <param name="data">The action payload</param>
            <returns>ActionResult with response data</returns>
            <remarks>
            Sample request:
            POST /api/addons/3fa85f64-5717-4562-b3fc-2c963f66afa6/custom-action
            {
              "action": "test",
              "parameters": { ... }
            }
            
            Sample response:
            {
              "addonId": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
              "action": "Custom action executed",
              "data": { ... }
            }
            </remarks>
            <response code="200">Returns the custom action response</response>
            <response code="400">If the addon ID is invalid</response>
            <response code="404">If the addon is not found</response>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.ValidateAddonAccess(System.Guid,System.Guid)">
            <summary>
            Validates whether the user has access to the specified addon instance
            </summary>
            <param name="addonInstanceId">The ID of the addon instance</param>
            <param name="userId">The ID of the user</param>
            <returns>True if the user has access; otherwise, false</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.GetAddonData(System.Guid)">
            <summary>
            Gets the data for the specified addon instance
            </summary>
            <param name="addonInstanceId">The ID of the addon instance</param>
            <returns>The addon data as a JsonElement, or null if not found</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.UpdateAddonData(System.Guid,System.String)">
            <summary>
            Updates the data for the specified addon instance
            </summary>
            <param name="addonInstanceId">The ID of the addon instance</param>
            <param name="data">The new data as a JSON string</param>
            <returns>True if the update was successful; otherwise, false</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.InvokeAddonCustomEndpoint(System.Guid)">
            <summary>
            Handles GET requests to dynamic addon endpoints
            </summary>
            <param name="addonId">The ID of the addon</param>
            <returns>ActionResult with response data</returns>
        </member>
        <member name="M:VelocityPlatform.API.Services.IAddonEndpointService.InvokeAddonCustomAction(System.Guid,System.Object)">
            <summary>
            Handles POST requests to dynamic addon actions
            </summary>
            <param name="addonId">The ID of the addon</param>
            <param name="data">The action payload</param>
            <returns>ActionResult with response data</returns>
        </member>
    </members>
</doc>
