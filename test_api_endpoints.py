#!/usr/bin/env python3
"""
VelocityPlatform API Endpoint Testing Script
Systematically tests all API endpoints and tracks results
"""

import requests
import json
import sys
import time
import random
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple

class APITester:
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.auth_token = None
        self.test_results = []
        self.test_user_id = None
        self.test_site_id = None
        self.test_tenant_id = None
        
    def log_result(self, endpoint: str, method: str, status_code: int, 
                   response_data: dict = None, error: str = None, notes: str = ""):
        """Log test result"""
        result = {
            "timestamp": datetime.now().isoformat(),
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": 200 <= status_code < 300,
            "response_data": response_data,
            "error": error,
            "notes": notes
        }
        self.test_results.append(result)
        
        # Print result
        status_emoji = "✅" if result["success"] else "❌"
        print(f"{status_emoji} {method} {endpoint} - {status_code} {notes}")
        if error:
            print(f"   Error: {error}")
            
    def test_endpoint(self, endpoint: str, method: str = "GET", 
                     data: dict = None, headers: dict = None, 
                     auth_required: bool = False) -> Tuple[int, dict]:
        """Test a single endpoint"""
        url = f"{self.base_url}{endpoint}"
        
        # Setup headers
        request_headers = {"Content-Type": "application/json"}
        if headers:
            request_headers.update(headers)
            
        # Add auth token if required and available
        if auth_required and self.auth_token:
            request_headers["Authorization"] = f"Bearer {self.auth_token}"
            
        try:
            if method.upper() == "GET":
                response = self.session.get(url, headers=request_headers)
            elif method.upper() == "POST":
                response = self.session.post(url, json=data, headers=request_headers)
            elif method.upper() == "PUT":
                response = self.session.put(url, json=data, headers=request_headers)
            elif method.upper() == "DELETE":
                response = self.session.delete(url, headers=request_headers)
            else:
                raise ValueError(f"Unsupported method: {method}")
                
            # Parse response
            try:
                response_data = response.json()
            except:
                response_data = {"raw_response": response.text}
                
            self.log_result(endpoint, method, response.status_code, 
                          response_data, notes=f"Response: {response.reason}")
            
            return response.status_code, response_data
            
        except Exception as e:
            self.log_result(endpoint, method, 0, error=str(e))
            return 0, {"error": str(e)}
    
    def create_test_user(self) -> tuple[bool, str]:
        """Create a test user via registration"""
        # Create unique email to avoid conflicts
        timestamp = int(time.time())
        random_num = random.randint(1000, 9999)
        test_email = f"testuser{timestamp}{random_num}@example.com"

        register_data = {
            "email": test_email,
            "password": "TestPassword123!",
            "firstName": "Test",
            "lastName": "User",
            "acceptTerms": True
        }

        status_code, response = self.test_endpoint("/api/v1/Auth/register", "POST", register_data)

        if status_code in [200, 201]:
            print(f"✅ Test user created successfully: {test_email}")
            return True, test_email
        else:
            print(f"⚠️ Test user creation failed - Status: {status_code}")
            return False, test_email

    def confirm_user_email(self) -> bool:
        """Confirm user email using the token from logs"""
        # Token from the <NAME_EMAIL>
        token = "CfDJ8EjbdKbUnLBGjjTf5mNF9tUY31pVsFf6kacbhHFXwYRg9KgnHhLpY0vdhN7dBMb64KzNTGhyrWo+dPaIB9lpD5gXoFt+dugFX2QfLs1fvYw6Uxuze+sAYBXxCb44jRIfbM2bJQKTimCoUVfJwwaWtq5yZaDHscOKSpqyMzOfL2PdOP243aBWu3qEXKtHkoY/YbP2zC8XR7pHzEAZ6pcA/GhAtzBFSQdBrDKeAL7aatv5fQXShA973Wy97Hpt+GTMTKFw=="
        email = "<EMAIL>"

        # URL encode the token
        import urllib.parse
        encoded_token = urllib.parse.quote(token)

        endpoint = f"/api/v1/Auth/confirm-email?email={email}&token={encoded_token}"
        status_code, response = self.test_endpoint(endpoint, "GET")

        if status_code == 200:
            print(f"✅ Email confirmed successfully")
            return True
        else:
            print(f"❌ Email confirmation failed - Status: {status_code}")
            return False

    def login_and_get_token(self, email: str = None) -> bool:
        """Login and get authentication token"""
        if not email:
            email = "<EMAIL>"  # fallback

        login_data = {
            "email": email,
            "password": "TestPassword123!"
        }

        status_code, response = self.test_endpoint("/api/v1/Auth/login", "POST", login_data)

        if status_code == 200 and "data" in response and "token" in response["data"]:
            self.auth_token = response["data"]["token"]
            print(f"🔑 Authentication successful - Token obtained for {email}")
            return True
        else:
            print(f"❌ Authentication failed for {email} - Status: {status_code}")
            return False
    
    def test_basic_endpoints(self):
        """Test basic connectivity endpoints"""
        print("\n🔍 Testing Basic Connectivity...")
        
        # Test root endpoint
        self.test_endpoint("/")
        
        # Test health check
        self.test_endpoint("/health")
        
        # Test auth test endpoint
        self.test_endpoint("/api/v1/Auth/test")
        
    def test_auth_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints...")

        # Create a fresh test user
        user_created, test_email = self.create_test_user()

        if user_created:
            print(f"📧 Waiting 2 seconds for email confirmation token to be generated...")
            time.sleep(2)  # Give time for email to be sent and logged

            # For now, skip email confirmation and try to manually confirm in database
            # or create a bypass for testing
            print(f"⚠️ Skipping email confirmation for now - will try direct login")

            # Try login (will fail due to unconfirmed email, but we'll see the error)
            self.test_endpoint("/api/v1/Auth/login", "POST", {
                "email": test_email,
                "password": "TestPassword123!"
            })

        # Test with confirmed test user
        print(f"\n🔄 Testing with confirmed test user...")

        # Try login with the confirmed test user we created
        confirmed_users = [
            "<EMAIL>",  # Our confirmed test user
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        for email in confirmed_users:
            # Use the correct password for each user
            if email == "<EMAIL>":
                password = "password"  # From CheckAndCreateUser.cs
            else:
                password = "TestPassword123!"  # For other users

            login_data = {"email": email, "password": password}
            status_code, response = self.test_endpoint("/api/v1/Auth/login", "POST", login_data)

            if status_code == 200 and "data" in response and "token" in response["data"]:
                self.auth_token = response["data"]["token"]
                print(f"🔑 Successfully authenticated with {email}")
                break

        # Test protected endpoints if we have a token
        if self.auth_token:
            self.test_endpoint("/api/v1/Auth/me", "GET", auth_required=True)
            self.test_endpoint("/api/v1/Auth/api-key", "GET", auth_required=True)
        else:
            print("⚠️ No authentication token available - skipping protected endpoint tests")
        
    def test_user_endpoints(self):
        """Test user management endpoints"""
        print("\n👤 Testing User Endpoints...")
        
        if not self.auth_token:
            print("⚠️ Skipping user tests - no auth token")
            return
            
        # Test user endpoints
        self.test_endpoint("/api/v1/Users/<USER>", "GET", auth_required=True)
        self.test_endpoint("/api/v1/Users", "GET", auth_required=True)
        
        # Test user preferences (might need user ID)
        test_user_id = "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a"  # From previous tests
        self.test_endpoint(f"/api/v1/Users/<USER>/preferences", "GET", auth_required=True)
        
    def test_site_endpoints(self):
        """Test site management endpoints"""
        print("\n🌐 Testing Site Endpoints...")
        
        if not self.auth_token:
            print("⚠️ Skipping site tests - no auth token")
            return
            
        # Test sites
        self.test_endpoint("/api/v1/Sites", "GET", auth_required=True)
        
    def test_addon_endpoints(self):
        """Test addon endpoints"""
        print("\n🔌 Testing Addon Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping addon tests - no auth token")
            return

        # Test addon endpoints
        self.test_endpoint("/api/v1/AddonDefinitions", "GET", auth_required=True)

        # Note: /pending endpoint requires admin role - 403 is expected for regular users
        status_code, response = self.test_endpoint("/api/v1/AddonDefinitions/pending", "GET", auth_required=True)
        if status_code == 403:
            print("   ℹ️ 403 Forbidden is expected - endpoint requires admin role")

        self.test_endpoint("/api/v1/Addons/global", "GET", auth_required=True)
        self.test_endpoint("/api/v1/Addons/purchases", "GET", auth_required=True)

    def test_pages_endpoints(self):
        """Test pages endpoints"""
        print("\n📄 Testing Pages Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping pages tests - no auth token")
            return

        # Get a site ID first
        if not self.test_site_id:
            status_code, response = self.test_endpoint("/api/v1/Sites", "GET", auth_required=True)
            if status_code == 200 and response.get("data", {}).get("data"):
                sites = response["data"]["data"]
                if sites:
                    self.test_site_id = sites[0]["id"]
                    print(f"   Using site ID: {self.test_site_id}")

        if self.test_site_id:
            # Test pages endpoints
            self.test_endpoint(f"/api/v1/Sites/{self.test_site_id}/Pages", "GET", auth_required=True)

            # Test create page
            page_data = {
                "title": "Test Page",
                "slug": f"test-page-{int(time.time())}",
                "content": "<h1>Test Page Content</h1>",
                "isPublished": False
            }
            status_code, response = self.test_endpoint(f"/api/v1/Sites/{self.test_site_id}/Pages", "POST", page_data, auth_required=True)

            # If page created successfully, test other operations
            if status_code in [200, 201] and response.get("data", {}).get("id"):
                page_id = response["data"]["id"]
                self.test_endpoint(f"/api/v1/Sites/{self.test_site_id}/Pages/{page_id}", "GET", auth_required=True)

                # Test update page
                update_data = {"title": "Updated Test Page", "content": "<h1>Updated Content</h1>"}
                self.test_endpoint(f"/api/v1/Sites/{self.test_site_id}/Pages/{page_id}", "PUT", update_data, auth_required=True)

                # Test delete page
                self.test_endpoint(f"/api/v1/Sites/{self.test_site_id}/Pages/{page_id}", "DELETE", auth_required=True)
        else:
            print("   ⚠️ No site available for pages testing")

    def test_components_endpoints(self):
        """Test components endpoints"""
        print("\n🧩 Testing Components Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping components tests - no auth token")
            return

        # Test components endpoints
        self.test_endpoint("/api/v1/Components", "GET", auth_required=True)

        # Test create component
        component_data = {
            "name": f"Test Component {int(time.time())}",
            "description": "Test component description",
            "category": "Custom",  # Required field
            "htmlTemplate": "<div>Test Component</div>",
            "cssStyles": ".test { color: red; }",
            "javaScriptCode": "console.log('test');",
            "isGlobal": False
        }
        status_code, response = self.test_endpoint("/api/v1/Components", "POST", component_data, auth_required=True)

        # If component created successfully, test other operations
        if status_code in [200, 201] and response.get("data", {}).get("id"):
            component_id = response["data"]["id"]
            self.test_endpoint(f"/api/v1/Components/{component_id}", "GET", auth_required=True)

            # Test update component
            update_data = {"name": "Updated Test Component", "description": "Updated description"}
            self.test_endpoint(f"/api/v1/Components/{component_id}", "PUT", update_data, auth_required=True)

            # Test delete component
            self.test_endpoint(f"/api/v1/Components/{component_id}", "DELETE", auth_required=True)

    def test_admin_endpoints(self):
        """Test admin endpoints"""
        print("\n👑 Testing Admin Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping admin tests - no auth token")
            return

        # Test admin endpoints (expect 403 for non-admin users)
        endpoints = [
            "/api/v1/Admin/users",
            "/api/v1/Admin/system-health",
            "/api/v1/Admin/configurations/test-key"  # Fixed: configurations (plural)
        ]

        for endpoint in endpoints:
            status_code, response = self.test_endpoint(endpoint, "GET", auth_required=True)
            if status_code == 403:
                print(f"   ℹ️ 403 Forbidden is expected for {endpoint} - requires admin role")

    def test_files_endpoints(self):
        """Test files endpoints"""
        print("\n📁 Testing Files Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping files tests - no auth token")
            return

        # Test files endpoints
        self.test_endpoint("/api/v1/Files", "GET", auth_required=True)

        # Test file upload (would need actual file data in real scenario)
        # For now, just test the endpoint structure

    def test_payments_endpoints(self):
        """Test payments endpoints"""
        print("\n💳 Testing Payments Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping payments tests - no auth token")
            return

        # Test payments endpoints
        self.test_endpoint("/api/v1/Payments", "GET", auth_required=True)

        # Test webhook endpoint (POST only)
        webhook_data = {
            "EventType": "payment.completed",  # Fixed: PascalCase for C# DTO
            "Data": {"paymentId": "test-payment-123"}  # Fixed: PascalCase for C# DTO
        }
        self.test_endpoint("/api/v1/Payments/webhook", "POST", webhook_data, auth_required=False)

    def test_subscriptions_endpoints(self):
        """Test subscriptions endpoints"""
        print("\n📋 Testing Subscriptions Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping subscriptions tests - no auth token")
            return

        # Test subscriptions endpoints
        self.test_endpoint("/api/v1/Subscriptions", "GET", auth_required=True)
        self.test_endpoint("/api/v1/Subscriptions/plans", "GET", auth_required=True)

    def test_security_endpoints(self):
        """Test security endpoints"""
        print("\n🔒 Testing Security Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping security tests - no auth token")
            return

        # Test security endpoints
        self.test_endpoint("/api/v1/Security/scans", "GET", auth_required=True)

        # Test vulnerability scan (might require specific data)
        scan_data = {
            "targetUrl": "https://example.com",
            "scanDepth": 1,
            "includeDependencies": False
        }
        self.test_endpoint("/api/v1/Security/scans", "POST", scan_data, auth_required=True)

    def test_tenant_endpoints(self):
        """Test tenant endpoints"""
        print("\n🏢 Testing Tenant Endpoints...")

        if not self.auth_token:
            print("⚠️ Skipping tenant tests - no auth token")
            return

        # Test tenant endpoints
        self.test_endpoint("/api/v1/Tenant", "GET", auth_required=True)
        self.test_endpoint("/api/v1/Tenants", "GET", auth_required=True)

    def run_all_tests(self):
        """Run all endpoint tests"""
        print("🚀 Starting VelocityPlatform API Endpoint Testing")
        print(f"📍 Base URL: {self.base_url}")
        print(f"⏰ Started at: {datetime.now().isoformat()}")

        # Run test suites
        self.test_basic_endpoints()
        self.test_auth_endpoints()
        self.test_user_endpoints()
        self.test_site_endpoints()
        self.test_addon_endpoints()
        self.test_pages_endpoints()
        self.test_components_endpoints()
        self.test_admin_endpoints()
        self.test_files_endpoints()
        self.test_payments_endpoints()
        self.test_subscriptions_endpoints()
        self.test_security_endpoints()
        self.test_tenant_endpoints()

        # Print summary
        self.print_summary()
        
    def print_summary(self):
        """Print test results summary"""
        print("\n" + "="*60)
        print("📊 TEST RESULTS SUMMARY")
        print("="*60)
        
        total_tests = len(self.test_results)
        successful_tests = len([r for r in self.test_results if r["success"]])
        failed_tests = total_tests - successful_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Successful: {successful_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(successful_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%")
        
        # Show failed tests
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   {result['method']} {result['endpoint']} - {result['status_code']}")
                    if result["error"]:
                        print(f"      Error: {result['error']}")
        
        # Save results to file
        with open("api_test_results.json", "w") as f:
            json.dump(self.test_results, f, indent=2)
        print(f"\n💾 Detailed results saved to: api_test_results.json")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
