using System;
using Npgsql;

namespace PasswordSaltFixer;

class Program
{
    static void Main()
    {
        var connectionString = "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1";
        
        try
        {
            using var connection = new NpgsqlConnection(connectionString);
            connection.Open();
            
            Console.WriteLine("Connected to database successfully!");
            
            // Make PasswordSalt nullable
            var sql = "ALTER TABLE \"AspNetUsers\" ALTER COLUMN \"PasswordSalt\" DROP NOT NULL;";
            
            using var command = new NpgsqlCommand(sql, connection);
            command.ExecuteNonQuery();
            
            Console.WriteLine("Successfully made PasswordSalt column nullable!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error: {ex.Message}");
        }
    }
}
