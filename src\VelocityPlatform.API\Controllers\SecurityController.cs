using VelocityPlatform.Models.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq; // Added for .Select()
using System.Threading.Tasks;
using VelocityPlatform.Business.Services;
using VelocityPlatform.Models.Entities;
using VelocityPlatform.Data;
using Microsoft.Extensions.Logging;

namespace VelocityPlatform.API.Controllers
{
    /// <summary>
    /// Controller for handling security-related operations
    /// </summary>
    [Route("api/v1/Security")]
    [ApiController]
    [Authorize] // Changed from Admin-only to regular auth for testing
    public class SecurityController : BaseController
    {
        private readonly IGDPRComplianceService _gdprService;
        private readonly IVulnerabilityScanService _vulnerabilityScanService;

        public SecurityController(
            IGDPRComplianceService gdprService,
            IVulnerabilityScanService vulnerabilityScanService,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context,
            ILogger<SecurityController> logger)
            : base(context, logger, tenantProvider)
        {
            _gdprService = gdprService ?? throw new ArgumentNullException(nameof(gdprService));
            _vulnerabilityScanService = vulnerabilityScanService ?? throw new ArgumentNullException(nameof(vulnerabilityScanService));
        }

        private GDPRReportDto MapToGDPRReportDto(GDPRReport report)
        {
            if (report == null) return null!;
            return new GDPRReportDto
            {
                Id = report.Id,
                TenantId = report.TenantId,
                ReportType = report.ReportType,
                GeneratedAt = report.GeneratedAt,
                Content = report.Content,
                ContentType = report.ContentType,
                FileName = report.FileName
            };
        }

        private SecurityEventDto MapToSecurityEventDto(SecurityEvent securityEvent)
        {
            if (securityEvent == null) return null!;
            return new SecurityEventDto
            {
                Id = securityEvent.Id,
                TenantId = securityEvent.TenantId,
                UserId = securityEvent.UserId,
                EventType = securityEvent.EventType,
                Severity = securityEvent.Severity,
                Description = securityEvent.Description,
                IpAddress = securityEvent.IpAddress?.ToString(),
                UserAgent = securityEvent.UserAgent,
                AdditionalData = securityEvent.AdditionalData, // Assuming direct assignment or simple conversion
                CreatedAt = securityEvent.CreatedAt,
                Timestamp = securityEvent.Timestamp
            };
        }

        private VulnerabilityScanDto MapToVulnerabilityScanDto(VulnerabilityScan scan)
        {
            if (scan == null) return null!;
            return new VulnerabilityScanDto
            {
                Id = scan.Id,
                TenantId = scan.TenantId,
                TargetUrl = scan.TargetUrl,
                ScanDepth = scan.ScanDepth,
                IncludeDependencies = scan.IncludeDependencies,
                StartedAt = scan.StartedAt,
                CompletedAt = scan.CompletedAt,
                Status = scan.Status,
                ScanResults = scan.ScanResults
            };
        }

        /// <summary>
        /// Retrieves GDPR compliance reports for the current tenant
        /// </summary>
        /// <returns>List of GDPR reports</returns>
        [HttpGet("gdpr")]
        public async Task<ActionResult<ApiResponse<List<GDPRReportDto>>>> GetGDPRReports()
        {
            // var tenantId = GetCurrentTenantId(); // Use actual tenant ID from context
             var tenantId = Guid.Parse("00000000-0000-0000-0000-000000000001"); // Placeholder
            var reports = await _gdprService.GetReportsAsync(tenantId);
            var reportDtos = reports.Select(MapToGDPRReportDto).ToList();
            return Ok(ApiResponse(reportDtos, "GDPR reports retrieved successfully"));
        }

        /// <summary>
        /// Retrieves security audit logs
        /// </summary>
        /// <returns>List of security events</returns>
        [HttpGet("logs")]
        public ActionResult<ApiResponse<List<SecurityEventDto>>> GetAuditLogs()
        {
            // This is mock data. In a real scenario, this would come from a service.
            var mockEvents = new List<SecurityEvent>
            {
                new SecurityEvent
                {
                    Id = Guid.NewGuid(),
                    Timestamp = DateTime.UtcNow,
                    CreatedAt = DateTime.UtcNow,
                    EventType = "SecurityController.GetAuditLogs called",
                    Description = "Retrieved audit logs (mock data)",
                    UserId = null, // Or GetCurrentUserId() if applicable
                    TenantId = GetCurrentTenantId(),
                    Severity = "Info",
                    IpAddress = HttpContext.Connection.RemoteIpAddress
                }
            };
            var eventDtos = mockEvents.Select(MapToSecurityEventDto).ToList();
            return Ok(ApiResponse(eventDtos, "Security audit logs retrieved successfully"));
        }

        /// <summary>
        /// Get all vulnerability scans for the current tenant
        /// </summary>
        /// <returns>List of vulnerability scans</returns>
        [HttpGet("scans")]
        [Authorize] // Override class-level admin requirement for this endpoint
        public async Task<ActionResult<ApiResponse<object>>> GetVulnerabilityScans([FromQuery] int pageNumber = 1, [FromQuery] int pageSize = 10)
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                _logger.LogInformation("Getting vulnerability scans for tenant {TenantId}", tenantId);

                // Mock response for now - in a real implementation, this would fetch from the vulnerability scan service
                var scans = new
                {
                    Data = new List<object>
                    {
                        new
                        {
                            Id = Guid.NewGuid(),
                            TargetUrl = "https://example.com",
                            Status = "Completed",
                            StartedAt = DateTime.UtcNow.AddHours(-2),
                            CompletedAt = DateTime.UtcNow.AddHours(-1),
                            VulnerabilitiesFound = 0,
                            Severity = "Low"
                        }
                    },
                    TotalCount = 1,
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    TotalPages = 1
                };

                return Ok(ApiResponse(scans, "Vulnerability scans retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vulnerability scans");
                return StatusCode(500, ErrorResponse<object>("An error occurred while retrieving vulnerability scans"));
            }
        }

        /// <summary>
        /// Start a new vulnerability scan
        /// </summary>
        /// <param name="request">Vulnerability scan request parameters</param>
        /// <returns>Vulnerability scan result</returns>
        [HttpPost("scans")]
        [Authorize] // Override class-level admin requirement for this endpoint
        public async Task<ActionResult<ApiResponse<object>>> StartVulnerabilityScan([FromBody] VulnerabilityScanRequestDto request)
        {
            try
            {
                if (request == null)
                    return BadRequest(ErrorResponse("Invalid request body"));

                if (!Uri.IsWellFormedUriString(request.TargetUrl, UriKind.Absolute))
                    return BadRequest(ErrorResponse("Invalid target URL format"));

                var tenantId = GetCurrentTenantId();
                _logger.LogInformation("Starting vulnerability scan for tenant {TenantId}", tenantId);

                // Mock response for now
                var scan = new
                {
                    Id = Guid.NewGuid(),
                    TargetUrl = request.TargetUrl,
                    Status = "Started",
                    StartedAt = DateTime.UtcNow,
                    ScanDepth = request.ScanDepth,
                    IncludeDependencies = request.IncludeDependencies
                };

                return CreatedAtAction(nameof(GetVulnerabilityScans), new { }, ApiResponse(scan, "Vulnerability scan started successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error starting vulnerability scan");
                return StatusCode(500, ErrorResponse("An error occurred while starting the vulnerability scan"));
            }
        }

        /// <summary>
        /// Triggers a vulnerability scan on the specified target URL (legacy endpoint)
        /// </summary>
        /// <param name="request">Vulnerability scan request parameters</param>
        /// <returns>Vulnerability scan result</returns>
        [HttpPost("scan")]
        [Authorize(Policy = "AdminOnly")]
        public async Task<ActionResult<ApiResponse<VulnerabilityScanDto>>> TriggerVulnerabilityScan([FromBody] VulnerabilityScanRequestDto request)
        {
            if (request == null)
                return BadRequest(ErrorResponse<VulnerabilityScanDto>("Invalid request body"));
            
            if (!Uri.IsWellFormedUriString(request.TargetUrl, UriKind.Absolute))
                return BadRequest(ErrorResponse<VulnerabilityScanDto>("Invalid target URL format"));
            
            if (request.ScanDepth < 1 || request.ScanDepth > 10)
                return BadRequest(ErrorResponse<VulnerabilityScanDto>("Scan depth must be between 1 and 10"));
            
            var tenantId = _tenantProvider.GetTenantId();
            var scanEntity = await _vulnerabilityScanService.ScanAsync(tenantId, request.TargetUrl, request.ScanDepth, request.IncludeDependencies);
            var scanDto = MapToVulnerabilityScanDto(scanEntity);
            // Consider if the Get action for a single scan exists for CreatedAtAction
            // If not, just return Ok(scanDto) or new ObjectResult(scanDto) { StatusCode = 201 }
            return StatusCode(201, ApiResponse(scanDto, "Vulnerability scan triggered successfully"));
        }
    }
}