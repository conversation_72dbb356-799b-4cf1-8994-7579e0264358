{"version": 2, "dgSpecHash": "Wcu+SJ7qMmg=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\My Ideas\\VWPlatformweb\\src\\VelocityPlatform.Data\\DatabaseFixer\\DatabaseFixer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\8.0.2\\microsoft.extensions.dependencyinjection.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\8.0.2\\microsoft.extensions.logging.abstractions.8.0.2.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\npgsql\\9.0.3\\npgsql.9.0.3.nupkg.sha512"], "logs": []}