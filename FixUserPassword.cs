using System;
using System.Threading.Tasks;
using Npgsql;
using Microsoft.AspNetCore.Identity;
using VelocityPlatform.Models.Entities;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1";
        
        try
        {
            Console.WriteLine("Fixing user password hash...");
            
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            
            Console.WriteLine("Connected to database successfully.");
            
            // Create a password hasher
            var passwordHasher = new PasswordHasher<User>();
            var dummyUser = new User(); // We need a User instance for the hasher
            
            // Hash the password "password" using ASP.NET Core Identity's hasher
            var hashedPassword = passwordHasher.HashPassword(dummyUser, "password");
            
            Console.WriteLine($"Generated password hash: {hashedPassword}");
            
            // Update the test user's password hash
            var updateSql = @"
                UPDATE ""AspNetUsers"" 
                SET ""PasswordHash"" = @passwordHash
                WHERE ""Email"" = '<EMAIL>';";
            
            using var updateCommand = new NpgsqlCommand(updateSql, connection);
            updateCommand.Parameters.AddWithValue("passwordHash", hashedPassword);
            
            var rowsAffected = await updateCommand.ExecuteNonQueryAsync();
            Console.WriteLine($"✓ Updated {rowsAffected} user records with correct password hash.");
            
            Console.WriteLine("✅ Password fix completed successfully!");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
