[{"timestamp": "2025-06-11T00:35:41.909890", "endpoint": "/", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Velocity Platform API is running. Use /swagger for documentation"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:41.923084", "endpoint": "/health", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Healthy"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:42.198324", "endpoint": "/api/v1/Auth/test", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Test successful", "data": {"message": "Hello World from AuthController v1"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:43.200435", "endpoint": "/api/v1/Auth/register", "method": "POST", "status_code": 201, "success": true, "response_data": {"success": true, "message": "Registration successful. Please check your email to confirm your account.", "data": {"success": true, "token": "", "refreshToken": "", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "1d65476f-456d-4308-98ba-4102b9cbe65d", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:35:45.399674", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 400, "success": false, "response_data": {"success": false, "message": "Email not confirmed. Please check your inbox.", "data": null}, "error": null, "notes": "Response: Bad Request"}, {"timestamp": "2025-06-11T00:35:45.711925", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 401, "success": false, "response_data": {"success": false, "message": "Invalid email or password.", "data": null}, "error": null, "notes": "Response: Unauthorized"}, {"timestamp": "2025-06-11T00:35:45.891271", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Login successful.", "data": {"success": true, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j7aIDvjgxX0qdoOoEWvKaRu5bnd3NDQJmZi736tqREg", "refreshToken": "8fcohYSdbSy7PwE3EXJui+uelcZf7G/XeJ5ztOeE9Co=", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.019873", "endpoint": "/api/v1/Auth/me", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "User profile retrieved successfully.", "data": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userId": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userName": "testuser", "email": "<EMAIL>", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "firstName": "Test", "avatarUrl": null, "lastName": "User", "preferences": {}, "profileData": null, "role": 2, "permissions": [], "isAnonymized": false, "anonymizedDate": null}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.034664", "endpoint": "/api/v1/Auth/api-key", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "API key retrieved successfully.", "data": {"apiKey": "APIKEY-e0d5169b-b25a-4cfb-a4c1-95c764e7f69a-5c3d6f58-c0f8-426a-b81d-d17b936857dc"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.083531", "endpoint": "/api/v1/Users/<USER>", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.151482", "endpoint": "/api/v1/Users", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.208375", "endpoint": "/api/v1/Users/<USER>/preferences", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.353256", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.503639", "endpoint": "/api/v1/AddonDefinitions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.510498", "endpoint": "/api/v1/AddonDefinitions/pending", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:35:46.656765", "endpoint": "/api/v1/Addons/global", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.789994", "endpoint": "/api/v1/Addons/purchases", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.842954", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:46.978802", "endpoint": "/api/v1/Components", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:47.132231", "endpoint": "/api/v1/Components", "method": "POST", "status_code": 201, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:35:47.137156", "endpoint": "/api/v1/Admin/users", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:35:47.143465", "endpoint": "/api/v1/Admin/system-health", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:35:47.147826", "endpoint": "/api/v1/Admin/configurations/test-key", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:35:47.164530", "endpoint": "/api/v1/Files", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:47.190732", "endpoint": "/api/v1/Payments", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:47.227197", "endpoint": "/api/v1/Payments/webhook", "method": "POST", "status_code": 400, "success": false, "response_data": {"type": "https://tools.ietf.org/html/rfc9110#section-15.5.1", "title": "One or more validation errors occurred.", "status": 400, "errors": {"Json": ["Raw JSON payload is required if you expect it here."], "SignatureHeader": ["Signature header is required for webhook verification."]}, "traceId": "00-b403c8f55885913341518c9b32190756-3609271352d39569-00"}, "error": null, "notes": "Response: Bad Request"}, {"timestamp": "2025-06-11T00:35:47.354335", "endpoint": "/api/v1/Subscriptions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:47.404112", "endpoint": "/api/v1/Subscriptions/plans", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Subscription plans retrieved successfully", "data": []}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:47.414576", "endpoint": "/api/v1/Security/scans", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:35:47.419569", "endpoint": "/api/v1/Security/scans", "method": "POST", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:35:47.442506", "endpoint": "/api/v1/Tenant", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:35:47.445376", "endpoint": "/api/v1/Tenants", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}]