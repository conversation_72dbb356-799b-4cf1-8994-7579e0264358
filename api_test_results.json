[{"timestamp": "2025-06-11T00:43:40.222820", "endpoint": "/", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Velocity Platform API is running. Use /swagger for documentation"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:40.224565", "endpoint": "/health", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Healthy"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:40.228020", "endpoint": "/api/v1/Auth/test", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Test successful", "data": {"message": "Hello World from AuthController v1"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:40.515307", "endpoint": "/api/v1/Auth/register", "method": "POST", "status_code": 201, "success": true, "response_data": {"success": true, "message": "Registration successful. Please check your email to confirm your account.", "data": {"success": true, "token": "", "refreshToken": "", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "22acf8e1-e1d8-4000-8ca4-bd5f655a681d", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:43:54.296358", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 400, "success": false, "response_data": {"success": false, "message": "Email not confirmed. Please check your inbox.", "data": null}, "error": null, "notes": "Response: Bad Request"}, {"timestamp": "2025-06-11T00:43:54.449760", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 401, "success": false, "response_data": {"success": false, "message": "Invalid email or password.", "data": null}, "error": null, "notes": "Response: Unauthorized"}, {"timestamp": "2025-06-11T00:43:54.550437", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Login successful.", "data": {"success": true, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.j90EhJ3WO2BwKLZfsvMsV_0Brrn8y8anH61IBBWIYBo", "refreshToken": "reRi9Y94L0WHaGlqOFypGFhqTfqfSWFt7i9gtmxxDig=", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.573149", "endpoint": "/api/v1/Auth/me", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "User profile retrieved successfully.", "data": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userId": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userName": "testuser", "email": "<EMAIL>", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "firstName": "Test", "avatarUrl": null, "lastName": "User", "preferences": {}, "profileData": null, "role": 2, "permissions": [], "isAnonymized": false, "anonymizedDate": null}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.575490", "endpoint": "/api/v1/Auth/api-key", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "API key retrieved successfully.", "data": {"apiKey": "APIKEY-e0d5169b-b25a-4cfb-a4c1-95c764e7f69a-f849d9cc-57d7-4387-af35-e31d96fd67ae"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.597764", "endpoint": "/api/v1/Users/<USER>", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.623669", "endpoint": "/api/v1/Users", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.648083", "endpoint": "/api/v1/Users/<USER>/preferences", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.708184", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.754095", "endpoint": "/api/v1/AddonDefinitions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.798013", "endpoint": "/api/v1/AddonDefinitions/pending", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.842025", "endpoint": "/api/v1/Addons/global", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.890410", "endpoint": "/api/v1/Addons/purchases", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:54.928754", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.041558", "endpoint": "/api/v1/Components", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.151604", "endpoint": "/api/v1/Components", "method": "POST", "status_code": 201, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:43:55.196713", "endpoint": "/api/v1/Admin/users", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "All users retrieved successfully", "data": {"pageNumber": 1, "pageSize": 10, "totalPages": 2, "totalCount": 18, "data": [{"id": "22acf8e1-e1d8-4000-8ca4-bd5f655a681d", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:43:40.270057Z", "updatedAt": "2025-06-10T23:43:40.270053Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "91bdbe9d-229d-4744-a0b3-927ff2c25b73", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:42:51.76134Z", "updatedAt": "2025-06-10T23:42:51.761156Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "f3ad0513-c364-46a5-ab17-7ae225dfbf80", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:40:35.81285Z", "updatedAt": "2025-06-10T23:40:35.812674Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "4c1b6dfd-1dfc-4509-b82b-9305586f880b", "tenantId": "be1d82ff-901d-405d-80b9-60cff4f37b9d", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": true, "role": 1, "firstName": "API", "lastName": "Test", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:39:22.853559Z", "updatedAt": "2025-06-10T23:39:22.853559Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "1d65476f-456d-4308-98ba-4102b9cbe65d", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:35:42.570499Z", "updatedAt": "2025-06-10T23:35:42.57027Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "880f62bd-e87e-4e24-ae2b-c46a6124b5db", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:33:41.659449Z", "updatedAt": "2025-06-10T23:33:41.659213Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "fdbbf0fa-12e6-4bc6-859f-71dbbac3e3d3", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:26:35.845907Z", "updatedAt": "2025-06-10T23:26:35.845733Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "29a0cfed-f51e-4c12-b12d-77b9831f6cff", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:23:27.213092Z", "updatedAt": "2025-06-10T23:23:27.213086Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "d95e9253-4521-4006-a16d-b022d85d43ce", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:23:17.289953Z", "updatedAt": "2025-06-10T23:23:17.289708Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}, {"id": "9fb6087b-f02e-4e3a-aeda-b3623512dd49", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "userName": "<EMAIL>", "email": "<EMAIL>", "emailConfirmed": false, "role": 2, "firstName": "Test", "lastName": "User", "avatarUrl": null, "lastLoginAt": null, "isLockedOut": false, "lockedOutEnd": null, "isActive": true, "createdAt": "2025-06-10T23:20:25.794438Z", "updatedAt": "2025-06-10T23:20:25.794254Z", "phoneNumber": null, "phoneNumberConfirmed": false, "twoFactorEnabled": false}]}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.198733", "endpoint": "/api/v1/Admin/system-health", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.222301", "endpoint": "/api/v1/Admin/configurations/test-key", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.224768", "endpoint": "/api/v1/Files", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.231193", "endpoint": "/api/v1/Payments", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.238021", "endpoint": "/api/v1/Payments/webhook", "method": "POST", "status_code": 202, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: Accepted"}, {"timestamp": "2025-06-11T00:43:55.291008", "endpoint": "/api/v1/Subscriptions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.312358", "endpoint": "/api/v1/Subscriptions/plans", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Subscription plans retrieved successfully", "data": []}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.317450", "endpoint": "/api/v1/Security/scans", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.321290", "endpoint": "/api/v1/Security/scans", "method": "POST", "status_code": 201, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:43:55.325328", "endpoint": "/api/v1/Tenant", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:43:55.388619", "endpoint": "/api/v1/Tenants", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}]