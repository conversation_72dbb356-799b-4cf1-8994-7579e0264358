[{"timestamp": "2025-06-11T00:12:19.407915", "endpoint": "/", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Velocity Platform API is running. Use /swagger for documentation"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:19.409616", "endpoint": "/health", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Healthy"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:19.412800", "endpoint": "/api/v1/Auth/test", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Test successful", "data": {"message": "Hello World from AuthController v1"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:19.869302", "endpoint": "/api/v1/Auth/register", "method": "POST", "status_code": 201, "success": true, "response_data": {"success": true, "message": "Registration successful. Please check your email to confirm your account.", "data": {"success": true, "token": "", "refreshToken": "", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "8bf43fae-99f9-4951-9f3d-4a68ca9fdf30", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:12:21.900512", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 400, "success": false, "response_data": {"success": false, "message": "Email not confirmed. Please check your inbox.", "data": null}, "error": null, "notes": "Response: Bad Request"}, {"timestamp": "2025-06-11T00:12:22.051890", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 401, "success": false, "response_data": {"success": false, "message": "Invalid email or password.", "data": null}, "error": null, "notes": "Response: Unauthorized"}, {"timestamp": "2025-06-11T00:12:22.144822", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Login successful.", "data": {"success": true, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OcAY5JX4y02D5sLwdqlU3G0jTBFuBQWTIV31rgK6E7c", "refreshToken": "CUphF70W9CLlNaC59Jn+POA3CEWlG53f1YWDIZ0MlbI=", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.170154", "endpoint": "/api/v1/Auth/me", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "User profile retrieved successfully.", "data": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userId": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userName": "testuser", "email": "<EMAIL>", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "firstName": "Test", "avatarUrl": null, "lastName": "User", "preferences": {}, "profileData": null, "role": 2, "permissions": [], "isAnonymized": false, "anonymizedDate": null}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.172917", "endpoint": "/api/v1/Auth/api-key", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "API key retrieved successfully.", "data": {"apiKey": "APIKEY-e0d5169b-b25a-4cfb-a4c1-95c764e7f69a-9040a291-e30a-4e5d-9776-d50d65300e2c"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.196637", "endpoint": "/api/v1/Users/<USER>", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.227775", "endpoint": "/api/v1/Users", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.261332", "endpoint": "/api/v1/Users/<USER>/preferences", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.305990", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.356719", "endpoint": "/api/v1/AddonDefinitions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.359239", "endpoint": "/api/v1/AddonDefinitions/pending", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:12:22.403110", "endpoint": "/api/v1/Addons/global", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:12:22.448688", "endpoint": "/api/v1/Addons/purchases", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}]