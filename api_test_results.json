[{"timestamp": "2025-06-11T00:26:35.502869", "endpoint": "/", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Velocity Platform API is running. Use /swagger for documentation"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:35.507585", "endpoint": "/health", "method": "GET", "status_code": 200, "success": true, "response_data": {"raw_response": "Healthy"}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:35.625996", "endpoint": "/api/v1/Auth/test", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Test successful", "data": {"message": "Hello World from AuthController v1"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:36.276867", "endpoint": "/api/v1/Auth/register", "method": "POST", "status_code": 201, "success": true, "response_data": {"success": true, "message": "Registration successful. Please check your email to confirm your account.", "data": {"success": true, "token": "", "refreshToken": "", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "fdbbf0fa-12e6-4bc6-859f-71dbbac3e3d3", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:26:38.411009", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 400, "success": false, "response_data": {"success": false, "message": "Email not confirmed. Please check your inbox.", "data": null}, "error": null, "notes": "Response: Bad Request"}, {"timestamp": "2025-06-11T00:26:38.589270", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 401, "success": false, "response_data": {"success": false, "message": "Invalid email or password.", "data": null}, "error": null, "notes": "Response: Unauthorized"}, {"timestamp": "2025-06-11T00:26:38.699434", "endpoint": "/api/v1/Auth/login", "method": "POST", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Login successful.", "data": {"success": true, "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.6GciCXfyYCNa9GGk0jN5xhkFGRb7brVT6JjIl5GHvuM", "refreshToken": "O2qjMwXE+Yvfs/k5SsWoX4eummitRwrmDM80STrHfWs=", "refreshTokenExpiry": "0001-01-01T00:00:00", "errorMessage": null, "user": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "role": 2}}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:38.772131", "endpoint": "/api/v1/Auth/me", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "User profile retrieved successfully.", "data": {"id": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userId": "e0d5169b-b25a-4cfb-a4c1-95c764e7f69a", "userName": "testuser", "email": "<EMAIL>", "tenantId": "8aad2e94-cfc4-4a7d-a4ea-8c8955c00248", "firstName": "Test", "avatarUrl": null, "lastName": "User", "preferences": {}, "profileData": null, "role": 2, "permissions": [], "isAnonymized": false, "anonymizedDate": null}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:38.779201", "endpoint": "/api/v1/Auth/api-key", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "API key retrieved successfully.", "data": {"apiKey": "APIKEY-e0d5169b-b25a-4cfb-a4c1-95c764e7f69a-12971dd8-b1c1-4e10-9026-862465a228c1"}}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:38.814994", "endpoint": "/api/v1/Users/<USER>", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:38.855140", "endpoint": "/api/v1/Users", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:38.890116", "endpoint": "/api/v1/Users/<USER>/preferences", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:38.985770", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.066298", "endpoint": "/api/v1/AddonDefinitions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.069713", "endpoint": "/api/v1/AddonDefinitions/pending", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:26:39.159544", "endpoint": "/api/v1/Addons/global", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.223342", "endpoint": "/api/v1/Addons/purchases", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.272326", "endpoint": "/api/v1/Sites", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.397156", "endpoint": "/api/v1/Components", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.522061", "endpoint": "/api/v1/Components", "method": "POST", "status_code": 201, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: Created"}, {"timestamp": "2025-06-11T00:26:39.524972", "endpoint": "/api/v1/Admin/users", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}, {"timestamp": "2025-06-11T00:26:39.527804", "endpoint": "/api/v1/Admin/system-health", "method": "GET", "status_code": 404, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Not Found"}, {"timestamp": "2025-06-11T00:26:39.529254", "endpoint": "/api/v1/Admin/configuration/test-key", "method": "GET", "status_code": 404, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Not Found"}, {"timestamp": "2025-06-11T00:26:39.531023", "endpoint": "/api/v1/Files", "method": "GET", "status_code": 404, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Not Found"}, {"timestamp": "2025-06-11T00:26:39.545255", "endpoint": "/api/v1/Payments", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.567623", "endpoint": "/api/v1/Payments/webhook", "method": "POST", "status_code": 400, "success": false, "response_data": {"type": "https://tools.ietf.org/html/rfc9110#section-15.5.1", "title": "One or more validation errors occurred.", "status": 400, "errors": {"Json": ["Raw JSON payload is required if you expect it here."], "SignatureHeader": ["Signature header is required for webhook verification."]}, "traceId": "00-2c9a6601493d61c50ecb2e1d79687be4-fd2d312f3adab961-00"}, "error": null, "notes": "Response: Bad Request"}, {"timestamp": "2025-06-11T00:26:39.650930", "endpoint": "/api/v1/Subscriptions", "method": "GET", "status_code": 200, "success": true, "response_data": {"result": {}, "value": null}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.682765", "endpoint": "/api/v1/Subscriptions/plans", "method": "GET", "status_code": 200, "success": true, "response_data": {"success": true, "message": "Subscription plans retrieved successfully", "data": []}, "error": null, "notes": "Response: OK"}, {"timestamp": "2025-06-11T00:26:39.684992", "endpoint": "/api/v1/Security/scans", "method": "GET", "status_code": 404, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Not Found"}, {"timestamp": "2025-06-11T00:26:39.686743", "endpoint": "/api/v1/Security/scans", "method": "POST", "status_code": 404, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Not Found"}, {"timestamp": "2025-06-11T00:26:39.688051", "endpoint": "/api/v1/Tenant", "method": "GET", "status_code": 404, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Not Found"}, {"timestamp": "2025-06-11T00:26:39.689309", "endpoint": "/api/v1/Tenants", "method": "GET", "status_code": 403, "success": false, "response_data": {"raw_response": ""}, "error": null, "notes": "Response: Forbidden"}]