using System;
using System.Threading.Tasks;
using Npgsql;
using Microsoft.AspNetCore.Identity;
using VelocityPlatform.Models.Entities;

class Program
{
    static async Task Main(string[] args)
    {
        var connectionString = "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=$Jf6sSkfyPb&v7r1";
        
        try
        {
            Console.WriteLine("Checking and creating test user...");
            
            using var connection = new NpgsqlConnection(connectionString);
            await connection.OpenAsync();
            
            Console.WriteLine("Connected to database successfully.");
            
            // First, delete the existing test user
            var deleteSql = @"DELETE FROM ""AspNetUsers"" WHERE ""Email"" = '<EMAIL>';";
            using var deleteCommand = new NpgsqlCommand(deleteSql, connection);
            var deletedRows = await deleteCommand.ExecuteNonQueryAsync();
            Console.WriteLine($"Deleted {deletedRows} existing test user records.");
            
            // Create a password hasher
            var passwordHasher = new PasswordHasher<User>();
            var dummyUser = new User(); // We need a User instance for the hasher
            
            // Hash the password "password" using ASP.NET Core Identity's hasher
            var hashedPassword = passwordHasher.HashPassword(dummyUser, "password");
            
            Console.WriteLine($"Generated password hash: {hashedPassword}");
            
            // Get the default tenant ID
            var tenantSql = @"SELECT ""Id"" FROM ""Tenants"" LIMIT 1;";
            using var tenantCommand = new NpgsqlCommand(tenantSql, connection);
            var tenantId = await tenantCommand.ExecuteScalarAsync();
            
            if (tenantId == null)
            {
                Console.WriteLine("No tenant found. Creating default tenant...");
                var createTenantSql = @"
                    INSERT INTO ""Tenants"" (""Id"", ""Name"", ""Domain"", ""CreatedAt"", ""UpdatedAt"", ""IsActive"")
                    VALUES (@id, 'Default Tenant', 'localhost', @now, @now, true)
                    RETURNING ""Id"";";
                using var createTenantCommand = new NpgsqlCommand(createTenantSql, connection);
                var newTenantId = Guid.NewGuid();
                createTenantCommand.Parameters.AddWithValue("id", newTenantId);
                createTenantCommand.Parameters.AddWithValue("now", DateTime.UtcNow);
                tenantId = await createTenantCommand.ExecuteScalarAsync();
                Console.WriteLine($"Created default tenant with ID: {tenantId}");
            }
            
            // Create the test user
            var insertSql = @"
                INSERT INTO ""AspNetUsers"" (
                    ""Id"", ""Email"", ""UserName"", ""NormalizedEmail"", ""NormalizedUserName"",
                    ""EmailConfirmed"", ""PasswordHash"", ""SecurityStamp"", ""ConcurrencyStamp"",
                    ""PhoneNumberConfirmed"", ""TwoFactorEnabled"", ""LockoutEnabled"",
                    ""AccessFailedCount"", ""TenantId"", ""CreatedAt"", ""UpdatedAt"", ""IsActive""
                ) VALUES (
                    @id, @email, @username, @normalizedEmail, @normalizedUsername,
                    @emailConfirmed, @passwordHash, @securityStamp, @concurrencyStamp,
                    @phoneNumberConfirmed, @twoFactorEnabled, @lockoutEnabled,
                    @accessFailedCount, @tenantId, @createdAt, @updatedAt, @isActive
                );";
            
            using var insertCommand = new NpgsqlCommand(insertSql, connection);
            insertCommand.Parameters.AddWithValue("id", Guid.NewGuid());
            insertCommand.Parameters.AddWithValue("email", "<EMAIL>");
            insertCommand.Parameters.AddWithValue("username", "testuser");
            insertCommand.Parameters.AddWithValue("normalizedEmail", "<EMAIL>");
            insertCommand.Parameters.AddWithValue("normalizedUsername", "TESTUSER");
            insertCommand.Parameters.AddWithValue("emailConfirmed", true);
            insertCommand.Parameters.AddWithValue("passwordHash", hashedPassword);
            insertCommand.Parameters.AddWithValue("securityStamp", Guid.NewGuid().ToString());
            insertCommand.Parameters.AddWithValue("concurrencyStamp", Guid.NewGuid().ToString());
            insertCommand.Parameters.AddWithValue("phoneNumberConfirmed", false);
            insertCommand.Parameters.AddWithValue("twoFactorEnabled", false);
            insertCommand.Parameters.AddWithValue("lockoutEnabled", true);
            insertCommand.Parameters.AddWithValue("accessFailedCount", 0);
            insertCommand.Parameters.AddWithValue("tenantId", tenantId);
            insertCommand.Parameters.AddWithValue("createdAt", DateTime.UtcNow);
            insertCommand.Parameters.AddWithValue("updatedAt", DateTime.UtcNow);
            insertCommand.Parameters.AddWithValue("isActive", true);
            
            await insertCommand.ExecuteNonQueryAsync();
            Console.WriteLine("✓ Created new test user with correct password hash.");
            
            Console.WriteLine("✅ User creation completed successfully!");
            Console.WriteLine("Test credentials: <EMAIL> / password");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
