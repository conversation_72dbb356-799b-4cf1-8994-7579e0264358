using VelocityPlatform.Models.Enums;
using VelocityPlatform.Business.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;
using VelocityPlatform.Models.DTOs;
using VelocityPlatform.Models.Entities; // Still needed for nameof(Tenant)
using Microsoft.Extensions.Logging; // For logging
using VelocityPlatform.Data; // For VelocityPlatformDbContext and ITenantProvider

namespace VelocityPlatform.API.Controllers
{
    /// <summary>
    /// Controller for tenant management operations
    /// </summary>
    [ApiController]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/[controller]")]
    public class TenantController : BaseController // Inherit BaseController
    {
        private readonly ITenantIsolationService _tenantIsolationService;
        private readonly IAuditLogService _auditLogService;

        public TenantController(
            ITenantIsolationService tenantIsolationService, 
            IAuditLogService auditLogService,
            ILogger<TenantController> logger,
            ITenantProvider tenantProvider,
            VelocityPlatformDbContext context) : base(context, logger, tenantProvider)
        {
            _tenantIsolationService = tenantIsolationService ?? throw new ArgumentNullException(nameof(tenantIsolationService));
            _auditLogService = auditLogService ?? throw new ArgumentNullException(nameof(auditLogService));
        }

        /// <summary>
        /// Get current tenant information
        /// </summary>
        /// <returns>Current tenant details</returns>
        [HttpGet]
        [Authorize]
        public async Task<ActionResult<ApiResponse<object>>> GetCurrentTenant()
        {
            try
            {
                var tenantId = GetCurrentTenantId();
                _logger.LogInformation("Getting current tenant information for {TenantId}", tenantId);

                // Mock response for now - in a real implementation, this would fetch tenant details
                var tenant = new
                {
                    Id = tenantId,
                    Name = "Current Tenant",
                    Domain = "example.com",
                    Status = "Active",
                    CreatedAt = DateTime.UtcNow.AddDays(-30),
                    SubscriptionPlan = "Professional",
                    MaxSites = 10,
                    MaxUsers = 50
                };

                return Ok(ApiResponse(tenant, "Current tenant retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving current tenant");
                return StatusCode(500, ErrorResponse<object>("An error occurred while retrieving tenant information"));
            }
        }

        /// <summary>
        /// Enforces an isolation policy for a tenant
        /// </summary>
        /// <param name="tenantId">The ID of the tenant</param>
        /// <param name="policyDto">Isolation policy details</param>
        /// <returns>Action result indicating success or failure</returns>
        [HttpPut("{tenantId}/isolate")]
        [Authorize(Policy = "AdminOrOwner")] // Ensure this policy exists and is correctly configured
        public async Task<ActionResult<ApiResponse>> EnforceIsolation(string tenantId, [FromBody] IsolationPolicyDto policyDto)
        {
            try
            {
                if (policyDto == null || string.IsNullOrWhiteSpace(policyDto.PolicyType))
                {
                    _logger.LogWarning("EnforceIsolation called with invalid policy DTO for tenant {TenantId}", tenantId);
                    return BadRequest(ErrorResponse("Valid isolation policy details are required."));
                }

                Guid tenantGuid;
                if (!Guid.TryParse(tenantId, out tenantGuid))
                {
                    _logger.LogWarning("EnforceIsolation called with invalid tenant ID format: {TenantId}", tenantId);
                    return BadRequest(ErrorResponse("Invalid tenant ID format."));
                }
                
                // Enforce isolation policy
                await _tenantIsolationService.EnforceIsolationAsync(tenantGuid); // Assuming this method now takes Guid

                // Create audit log via service
                Guid? userId = GetCurrentUserId(); // Use GetCurrentUserId from BaseController
                
                await _auditLogService.CreateAuditLogAsync(
                    AuditAction.EnforceIsolationPolicy,
                    tenantGuid.ToString(), // Storing Guid as string in EntityId
                    nameof(Tenant),
                    userId,
                    $"Enforced isolation policy '{policyDto.PolicyType}' for tenant {tenantId}"
                );

                _logger.LogInformation("Isolation policy '{PolicyType}' enforced successfully for tenant {TenantId} by user {UserId}", policyDto.PolicyType, tenantId, userId);
                return Ok(ApiResponse<object>(null!, "Isolation policy enforced successfully"));
            }
            catch (ArgumentException ex) // Catch specific exceptions from services if they throw them
            {
                _logger.LogWarning(ex, "Argument exception while enforcing isolation for tenant {TenantId}", tenantId);
                return BadRequest(ErrorResponse(ex.Message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enforcing isolation policy for tenant {TenantId}", tenantId);
                return StatusCode(500, ErrorResponse($"An unexpected error occurred while enforcing the isolation policy: {ex.Message}"));
            }
        }
    }
}