# PowerShell script to add missing columns to AspNetUsers table
$connectionString = "Host=**************;Port=5432;Database=VWPLATFORMWEB;Username=PLATFORMDB;Password=`$Jf6sSkfyPb&v7r1"

# Load Npgsql assembly
Add-Type -Path "C:\Users\<USER>\.nuget\packages\npgsql\9.0.3\lib\net8.0\Npgsql.dll"

try {
    Write-Host "Connecting to database..."
    $connection = New-Object Npgsql.NpgsqlConnection($connectionString)
    $connection.Open()
    Write-Host "Connected successfully!"

    # Array of SQL commands to add missing columns
    $commands = @(
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "AvatarUrl" character varying(500);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ApiKey" character varying(100);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ConfirmationToken" character varying(500);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ConfirmationTokenExpiry" timestamp with time zone;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ResetToken" character varying(500);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ResetTokenExpiry" timestamp with time zone;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "PreferencesJson" text;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "ProfileData" text;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "IsActive" boolean DEFAULT true;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "CreatedAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "UpdatedAt" timestamp with time zone DEFAULT CURRENT_TIMESTAMP;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "TenantId" uuid;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "FirstName" character varying(100);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LastName" character varying(100);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "Role" character varying(50);',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "EmailVerified" boolean DEFAULT false;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LastLoginAt" timestamp with time zone;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "FailedLoginAttempts" integer DEFAULT 0;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "IsLockedOut" boolean DEFAULT false;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LockedUntil" timestamp with time zone;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "LastPasswordChange" timestamp with time zone;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "PasswordSalt" bytea;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "IsAnonymized" boolean DEFAULT false;',
        'ALTER TABLE "AspNetUsers" ADD COLUMN IF NOT EXISTS "AnonymizedDate" timestamp with time zone;'
    )

    foreach ($sql in $commands) {
        try {
            $command = New-Object Npgsql.NpgsqlCommand($sql, $connection)
            $command.ExecuteNonQuery() | Out-Null
            Write-Host "Executed: $sql"
        }
        catch {
            Write-Host "Error executing $sql : $($_.Exception.Message)"
        }
    }

    # Update existing users with default values
    $updateSql = @"
UPDATE "AspNetUsers" SET 
    "IsActive" = COALESCE("IsActive", true),
    "CreatedAt" = COALESCE("CreatedAt", CURRENT_TIMESTAMP),
    "UpdatedAt" = COALESCE("UpdatedAt", CURRENT_TIMESTAMP),
    "EmailVerified" = COALESCE("EmailVerified", "EmailConfirmed"),
    "FailedLoginAttempts" = COALESCE("FailedLoginAttempts", 0),
    "IsLockedOut" = COALESCE("IsLockedOut", false),
    "IsAnonymized" = COALESCE("IsAnonymized", false);
"@

    $updateCommand = New-Object Npgsql.NpgsqlCommand($updateSql, $connection)
    $rowsAffected = $updateCommand.ExecuteNonQuery()
    Write-Host "Updated $rowsAffected existing user records."

    Write-Host "Database fix completed successfully!"
}
catch {
    Write-Host "Error: $($_.Exception.Message)"
}
finally {
    if ($connection) {
        $connection.Close()
    }
}
