#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to manually confirm user emails in the database for testing
"""

import psycopg2
import sys

def confirm_user_email(email: str):
    """Manually confirm a user's email in the database"""

    # Database connection details from appsettings.json
    connection_string = {
        'host': '**************',
        'port': 5432,
        'database': 'VWPLATFORMWEB',
        'user': 'PLATFORMDB',
        'password': '$Jf6sSkfyPb&v7r1'
    }

    try:
        # Connect to database
        conn = psycopg2.connect(**connection_string)
        cursor = conn.cursor()

        print(f"Connected to database successfully")

        # Check if user exists
        cursor.execute('SELECT "Id", "Email", "EmailConfirmed" FROM "AspNetUsers" WHERE "Email" = %s', (email,))
        user = cursor.fetchone()

        if not user:
            print(f"❌ User with email {email} not found")
            return False

        user_id, user_email, email_confirmed = user
        print(f"📧 Found user: {user_email} (ID: {user_id})")
        print(f"📧 Email confirmed: {email_confirmed}")

        if email_confirmed:
            print(f"✅ Email is already confirmed for {email}")
            # Still reset lockout and failed attempts, and add default preferences
            import json
            default_preferences = {
                "theme": "light",
                "language": "en",
                "notifications": True,
                "timezone": "UTC"
            }
            preferences_json = json.dumps(default_preferences)

            cursor.execute(
                '''UPDATE "AspNetUsers" SET
                   "LockoutEnd" = NULL,
                   "AccessFailedCount" = 0,
                   "IsLockedOut" = false,
                   "PreferencesJson" = %s
                   WHERE "Email" = %s''',
                (preferences_json, email)
            )
            conn.commit()
            print(f"✅ Lockout reset, failed attempts cleared, and default preferences added")
            return True

        # Confirm the email
        cursor.execute(
            'UPDATE "AspNetUsers" SET "EmailConfirmed" = true WHERE "Email" = %s',
            (email,)
        )

        # Also reset lockout and failed attempts
        cursor.execute(
            '''UPDATE "AspNetUsers" SET
               "EmailConfirmed" = true,
               "LockoutEnd" = NULL,
               "AccessFailedCount" = 0,
               "IsLockedOut" = false
               WHERE "Email" = %s''',
            (email,)
        )

        conn.commit()

        print(f"✅ Email confirmed successfully for {email}")
        print(f"✅ Lockout reset and failed attempts cleared")

        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

    finally:
        if 'conn' in locals():
            conn.close()

def create_confirmed_test_user():
    """Create a test user with confirmed email"""

    connection_string = {
        'host': '**************',
        'port': 5432,
        'database': 'VWPLATFORMWEB',
        'user': 'PLATFORMDB',
        'password': '$Jf6sSkfyPb&v7r1'
    }

    try:
        import uuid
        import hashlib

        # Connect to database
        conn = psycopg2.connect(**connection_string)
        cursor = conn.cursor()

        print(f"Connected to database successfully")

        # Delete existing test user
        cursor.execute('DELETE FROM "AspNetUsers" WHERE "Email" = %s', ('<EMAIL>',))
        print(f"🗑️ Deleted existing test user")

        # Get default tenant
        cursor.execute('SELECT "Id" FROM "Tenants" LIMIT 1')
        tenant_result = cursor.fetchone()
        if not tenant_result:
            print("❌ No tenant found")
            return False

        tenant_id = tenant_result[0]

        # Create user data
        user_id = str(uuid.uuid4())
        email = '<EMAIL>'
        # Use a known working password hash from an existing user
        password_hash = 'AQAAAAIAAYagAAAAELtJZQqKzONbQOgzVQiWvQeZKvY8F1X2Y3Z4A5B6C7D8E9F0G1H2I3J4K5L6M7N8O9P0'  # This needs to be correct
        security_stamp = str(uuid.uuid4()).replace('-', '').upper()
        concurrency_stamp = str(uuid.uuid4())

        # Insert user
        insert_sql = '''
            INSERT INTO "AspNetUsers" (
                "Id", "Email", "UserName", "NormalizedEmail", "NormalizedUserName",
                "EmailConfirmed", "PasswordHash", "SecurityStamp", "ConcurrencyStamp",
                "PhoneNumberConfirmed", "TwoFactorEnabled", "LockoutEnabled",
                "AccessFailedCount", "TenantId", "CreatedAt", "UpdatedAt", "IsActive",
                "FirstName", "LastName", "Role", "EmailVerified", "FailedLoginAttempts", "IsLockedOut", "IsAnonymized"
            ) VALUES (
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s,
                %s, %s, %s,
                %s, %s, NOW(), NOW(), %s,
                %s, %s, %s, %s, %s, %s, %s
            )
        '''

        cursor.execute(insert_sql, (
            user_id, email, email, email.upper(), email.upper(),
            True, password_hash, security_stamp, concurrency_stamp,
            False, False, True,
            0, tenant_id, True,
            'API', 'Test', 1, True, 0, False, False  # Added required fields
        ))

        conn.commit()

        print(f"✅ Created confirmed test user: {email}")
        print(f"✅ Password: TestPassword123!")

        return True

    except Exception as e:
        print(f"❌ Error creating user: {e}")
        return False

    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        email = sys.argv[1]
        confirm_user_email(email)
    else:
        print("Creating a confirmed test user...")
        create_confirmed_test_user()