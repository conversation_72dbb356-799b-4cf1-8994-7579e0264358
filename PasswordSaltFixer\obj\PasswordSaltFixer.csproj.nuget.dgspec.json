{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\My Ideas\\VWPlatformweb\\PasswordSaltFixer\\PasswordSaltFixer.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\My Ideas\\VWPlatformweb\\PasswordSaltFixer\\PasswordSaltFixer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\My Ideas\\VWPlatformweb\\PasswordSaltFixer\\PasswordSaltFixer.csproj", "projectName": "PasswordSaltFixer", "projectPath": "C:\\Users\\<USER>\\Desktop\\My Ideas\\VWPlatformweb\\PasswordSaltFixer\\PasswordSaltFixer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\My Ideas\\VWPlatformweb\\PasswordSaltFixer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Npgsql": {"target": "Package", "version": "[8.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.200/PortableRuntimeIdentifierGraph.json"}}}}}