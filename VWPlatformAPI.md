# VelocityPlatform API Documentation

## Overview

The VelocityPlatform API is a comprehensive RESTful API for a multi-tenant website builder platform with an addon marketplace. This API provides complete functionality for user management, site building, component management, payment processing, and administrative operations.

**Base URL:** `http://localhost:5000` (Development)  
**API Version:** v1  
**Authentication:** JWT Bearer Token  
**Content Type:** `application/json`

## Table of Contents

1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Site Management](#site-management)
4. [Component System](#component-system)
5. [Addon System](#addon-system)
6. [Payment Processing](#payment-processing)
7. [Subscription Management](#subscription-management)
8. [Administrative Functions](#administrative-functions)
9. [Security Features](#security-features)
10. [Tenant Management](#tenant-management)
11. [File Management](#file-management)
12. [Error Handling](#error-handling)
13. [Rate Limiting](#rate-limiting)

## Authentication

### Base Route: `/api/v1/Auth`

#### Test Connection
```http
GET /api/v1/Auth/test
```
**Description:** Test endpoint to verify API connectivity  
**Authentication:** None  
**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "message": "Hello World from AuthController v1"
  },
  "message": "Test successful"
}
```

#### User Registration
```http
POST /api/v1/Auth/register
```
**Description:** Register a new user account  
**Authentication:** None  
**Rate Limit:** Applied

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe",
  "role": "PlatformUser",
  "acceptTerms": true
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "success": true,
    "user": {
      "id": "guid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "PlatformUser"
    }
  },
  "message": "Registration successful. Please check your email to confirm your account."
}
```

#### User Login
```http
POST /api/v1/Auth/login
```
**Description:** Authenticate user and receive JWT token  
**Authentication:** None  
**Rate Limit:** Applied

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "success": true,
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "refresh_token_here",
    "expiresAt": "2024-01-01T12:00:00Z",
    "user": {
      "id": "guid",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "role": "PlatformUser"
    }
  },
  "message": "Login successful"
}
```

#### Get Current User
```http
GET /api/v1/Auth/me
```
**Description:** Get current authenticated user profile  
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "userId": "guid",
    "userName": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "tenantId": "guid",
    "role": "PlatformUser"
  },
  "message": "User profile retrieved successfully"
}
```

#### Get API Key
```http
GET /api/v1/Auth/api-key
```
**Description:** Generate or retrieve API key for authenticated user  
**Authentication:** Bearer Token Required  
**Rate Limit:** Applied

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "apiKey": "api_key_here",
    "expiresAt": "2024-12-31T23:59:59Z",
    "isActive": true
  },
  "message": "API key retrieved successfully"
}
```

#### Refresh Token
```http
POST /api/v1/Auth/refresh-token
```
**Description:** Refresh JWT token using refresh token  
**Authentication:** None

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "token": "new_jwt_token",
    "refreshToken": "new_refresh_token",
    "expiresAt": "2024-01-01T12:00:00Z"
  },
  "message": "Token refreshed successfully"
}
```

#### Logout
```http
POST /api/v1/Auth/logout
```
**Description:** Logout user and revoke refresh token  
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Successfully logged out"
}
```

#### Forgot Password
```http
POST /api/v1/Auth/forgot-password
```
**Description:** Initiate password reset process  
**Authentication:** None

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "If an account with that email exists, a password reset link has been sent"
}
```

#### Reset Password
```http
POST /api/v1/Auth/reset-password
```
**Description:** Reset password using reset token  
**Authentication:** None

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "token": "reset_token",
  "newPassword": "NewSecurePassword123!"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Password has been reset successfully"
}
```

#### Change Password
```http
POST /api/v1/Auth/change-password
```
**Description:** Change password for authenticated user  
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "currentPassword": "CurrentPassword123!",
  "newPassword": "NewPassword123!"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

#### Confirm Email
```http
GET /api/v1/Auth/confirm-email?email={email}&token={token}
```
**Description:** Confirm user email address  
**Authentication:** None

**Query Parameters:**
- `email` (string, required): User email address
- `token` (string, required): Email confirmation token

**Response:** `200 OK`
```

#### Create User
```http
POST /api/v1/Users
```
**Description:** Create a new user (Admin only)
**Authentication:** Bearer Token Required (TenantAdmin or PlatformAdmin)

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "Jane",
  "lastName": "Smith",
  "role": "PlatformUser",
  "tenantId": "guid",
  "roles": ["PlatformUser"]
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "userId": "guid",
    "userName": "<EMAIL>",
    "firstName": "Jane",
    "lastName": "Smith",
    "email": "<EMAIL>",
    "tenantId": "guid",
    "role": "PlatformUser",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "User created successfully"
}
```

#### Update User
```http
PUT /api/v1/Users/<USER>
```
**Description:** Update user information
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Request Body:**
```json
{
  "firstName": "UpdatedFirstName",
  "lastName": "UpdatedLastName",
  "email": "<EMAIL>",
  "role": "PlatformUser"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": null,
  "message": "User updated successfully"
}
```

#### Deactivate User
```http
DELETE /api/v1/Users/<USER>
```
**Description:** Deactivate a user (PlatformAdmin only)
**Authentication:** Bearer Token Required (PlatformAdmin)

**Path Parameters:**
- `id` (guid, required): User ID

**Response:** `204 No Content`

#### Change User Password
```http
PUT /api/v1/Users/<USER>/change-password
```
**Description:** Change password for a specific user
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Request Body:**
```json
{
  "currentPassword": "CurrentPassword123!",
  "newPassword": "NewPassword123!"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": null,
  "message": "Password changed successfully"
}
```

#### Export User Data
```http
GET /api/v1/Users/<USER>/export-data
```
**Description:** Export user data (GDPR compliance)
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Response:** `200 OK` (File download)
**Content-Type:** `application/octet-stream`

#### Anonymize User
```http
POST /api/v1/Users/<USER>/anonymize
```
**Description:** Anonymize user data (GDPR compliance, PlatformAdmin only)
**Authentication:** Bearer Token Required (PlatformAdmin)

**Path Parameters:**
- `id` (guid, required): User ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": null,
  "message": "User data anonymized successfully"
}
```

#### Get User Consents
```http
GET /api/v1/Users/<USER>/consents
```
**Description:** Get user consent records
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "guid",
      "consentType": "DataProcessing",
      "isGranted": true,
      "grantedAt": "2024-01-01T00:00:00Z",
      "revokedAt": null
    }
  ],
  "message": "User consents retrieved successfully"
}
```

#### Update User Consent
```http
POST /api/v1/Users/<USER>/consent
```
**Description:** Update user consent
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Request Body:**
```json
{
  "consentType": "DataProcessing",
  "isGranted": true
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "Consent updated successfully"
}
```

#### Get User Preferences
```http
GET /api/v1/Users/<USER>/preferences
```
**Description:** Get user preferences
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "theme": "dark",
    "language": "en",
    "notifications": {
      "email": true,
      "push": false
    }
  },
  "message": "User preferences retrieved successfully"
}
```

#### Update User Preferences
```http
PUT /api/v1/Users/<USER>/preferences
```
**Description:** Update user preferences
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Request Body:**
```json
{
  "preferences": {
    "theme": "light",
    "language": "en",
    "notifications": {
      "email": true,
      "push": true
    }
  }
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "User preferences updated successfully"
}
```

## Site Management

### Base Route: `/api/v1/Sites`

#### Get Sites
```http
GET /api/v1/Sites
```
**Description:** Get paginated list of sites for current tenant
**Authentication:** Bearer Token Required

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Sort field
- `sortOrder` (string, default: "asc"): Sort order (asc/desc)
- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "name": "My Website",
        "subdomain": "mysite",
        "customDomain": "mysite.com",
        "status": "Published",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "ownerId": "guid",
        "ownerName": "John Doe",
        "tenantId": "guid",
        "publishedAt": "2024-01-01T12:00:00Z",
        "lastCompilationDate": "2024-01-01T11:00:00Z",
        "lastCompilationStatus": "Success",
        "lastDeploymentDate": "2024-01-01T12:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "Sites retrieved successfully"
}
```

#### Get Site by ID
```http
GET /api/v1/Sites/{id}
```
**Description:** Get specific site by ID
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Site ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "name": "My Website",
    "subdomain": "mysite",
    "customDomain": "mysite.com",
    "status": "Published",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "ownerId": "guid",
    "ownerName": "John Doe",
    "tenantId": "guid",
    "publishedAt": "2024-01-01T12:00:00Z",
    "lastCompilationDate": "2024-01-01T11:00:00Z",
    "lastCompilationStatus": "Success",
    "lastDeploymentDate": "2024-01-01T12:00:00Z"
  },
  "message": "Site retrieved successfully"
}
```

#### Create Site
```http
POST /api/v1/Sites
```
**Description:** Create a new site
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "name": "New Website",
  "subdomain": "newsite",
  "customDomain": "newsite.com",
  "templateId": "guid",
  "description": "My new website"
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "name": "New Website",
    "subdomain": "newsite",
    "customDomain": "newsite.com",
    "status": "Draft",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "ownerId": "guid",
    "ownerName": "John Doe",
    "tenantId": "guid"
  },
  "message": "Site created successfully"
}
```

#### Update Site
```http
PUT /api/v1/Sites/{id}
```
**Description:** Update site information
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Site ID

**Request Body:**
```json
{
  "name": "Updated Website Name",
  "subdomain": "updatedsite",
  "customDomain": "updatedsite.com",
  "description": "Updated description"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": null,
  "message": "Site updated successfully"
}
```

#### Delete Site
```http
DELETE /api/v1/Sites/{id}
```
**Description:** Delete a site
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Site ID

**Response:** `204 No Content`

#### Publish Site
```http
POST /api/v1/Sites/{id}/publish
```
**Description:** Publish a site
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Site ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "publishedAt": "2024-01-01T12:00:00Z",
    "publishedUrl": "https://mysite.velocityplatform.com"
  },
  "message": "Site published successfully"
}
```

#### Compile Site
```http
POST /api/v1/Sites/{id}/compile
```
**Description:** Compile a site
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Site ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "status": "Success",
    "compiledAt": "2024-01-01T11:00:00Z",
    "message": "Site compiled successfully",
    "warnings": [],
    "errors": []
  },
  "message": "Site compilation completed"
}
```

#### Get Site Versions
```http
GET /api/v1/Sites/{id}/versions
```
**Description:** Get site version history
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Site ID

**Query Parameters:**
- `page` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "guid",
      "siteId": "guid",
      "version": "1.0.0",
      "description": "Initial version",
      "createdAt": "2024-01-01T00:00:00Z",
      "createdBy": "guid",
      "isPublished": true,
      "publishedAt": "2024-01-01T12:00:00Z"
    }
  ],
  "message": "Site versions retrieved successfully"
}
```

## Component System

### Base Route: `/api/v1/Components`

#### Get Components
```http
GET /api/v1/Components
```
**Description:** Get paginated list of components
**Authentication:** Bearer Token Required

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 20): Items per page
- `category` (string, optional): Filter by category
- `searchTerm` (string, optional): Search term
- `includeGlobal` (bool, default: true): Include global components

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "name": "Header Component",
        "description": "Responsive header component",
        "category": "Navigation",
        "tags": ["header", "navigation", "responsive"],
        "htmlTemplate": "<header>...</header>",
        "cssStyles": ".header { ... }",
        "javaScriptCode": "// Component JS",
        "configurationSchema": {},
        "defaultConfiguration": {},
        "previewImageUrl": "https://example.com/preview.jpg",
        "isGlobal": true,
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 20,
    "totalPages": 1
  },
  "message": "Components retrieved successfully"
}
```

#### Create Component
```http
POST /api/v1/Components
```
**Description:** Create a new component
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "name": "New Component",
  "description": "A new custom component",
  "category": "Content",
  "tags": ["custom", "content"],
  "htmlTemplate": "<div class='custom-component'>Content</div>",
  "cssStyles": ".custom-component { padding: 20px; }",
  "javaScriptCode": "// Custom JS",
  "configurationSchema": {},
  "defaultConfiguration": {},
  "isGlobal": false
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "name": "New Component",
    "description": "A new custom component",
    "category": "Content",
    "tags": ["custom", "content"],
    "htmlTemplate": "<div class='custom-component'>Content</div>",
    "cssStyles": ".custom-component { padding: 20px; }",
    "javaScriptCode": "// Custom JS",
    "configurationSchema": {},
    "defaultConfiguration": {},
    "previewImageUrl": null,
    "isGlobal": false,
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "Component created successfully"
}
```

## Addon System

### Base Route: `/api/v1/AddonDefinitions` and `/api/v1/Addons`

#### Get Addon Definitions
```http
GET /api/v1/AddonDefinitions
```
**Description:** Get paginated list of addon definitions
**Authentication:** Bearer Token Required

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `category` (string, optional): Filter by category
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "name": "Contact Form Addon",
        "description": "Advanced contact form with validation",
        "version": "1.0.0",
        "category": "Forms",
        "price": 29.99,
        "currency": "USD",
        "isActive": true,
        "isApproved": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "Addon definitions retrieved successfully"
}
```

#### Get Pending Addon Definitions
```http
GET /api/v1/AddonDefinitions/pending
```
**Description:** Get pending addon definitions awaiting approval
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "name": "New Addon",
        "description": "Pending approval",
        "version": "1.0.0",
        "category": "Utilities",
        "price": 19.99,
        "currency": "USD",
        "isActive": false,
        "isApproved": false,
        "submittedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "Pending addon definitions retrieved successfully"
}
```

#### Get Global Addons
```http
GET /api/v1/Addons/global
```
**Description:** Get global addons available to all tenants
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "guid",
      "name": "Global Analytics Addon",
      "description": "Analytics tracking for all sites",
      "version": "2.1.0",
      "isGlobal": true,
      "isActive": true
    }
  ],
  "message": "Global addons retrieved successfully"
}
```

#### Get Addon Purchases
```http
GET /api/v1/Addons/purchases
```
**Description:** Get user's addon purchases
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "guid",
      "addonId": "guid",
      "addonName": "Contact Form Addon",
      "purchaseDate": "2024-01-01T00:00:00Z",
      "price": 29.99,
      "currency": "USD",
      "licenseKey": "license_key_here",
      "isActive": true
    }
  ],
  "message": "Addon purchases retrieved successfully"
}
```

## Payment Processing

### Base Route: `/api/v1/Payments`

#### Get Payment History
```http
GET /api/v1/Payments
```
**Description:** Get payment history for current tenant/user
**Authentication:** Bearer Token Required

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Sort field
- `sortOrder` (string, default: "asc"): Sort order (asc/desc)
- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "amount": 29.99,
        "currency": "USD",
        "description": "Contact Form Addon Purchase",
        "status": "Succeeded",
        "transactionId": "stripe_tx_12345",
        "createdAt": "2024-01-01T00:00:00Z",
        "processedAt": "2024-01-01T00:01:00Z",
        "tenantId": "guid",
        "userId": "guid",
        "subscriptionId": null,
        "invoiceId": null,
        "metadata": {}
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "Payment history retrieved successfully"
}
```

#### Process Payment
```http
POST /api/v1/Payments
```
**Description:** Process a payment
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "amount": 29.99,
  "currency": "USD",
  "description": "Addon Purchase",
  "paymentMethodId": "pm_1234567890",
  "subscriptionId": null,
  "invoiceId": null,
  "metadata": {
    "addonId": "guid"
  },
  "returnUrl": "https://mysite.com/payment/success"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "paymentId": "guid",
    "status": "Succeeded",
    "transactionId": "stripe_tx_12345",
    "amount": 29.99,
    "currency": "USD",
    "processedAt": "2024-01-01T00:01:00Z",
    "isSuccess": true,
    "message": "Payment processed successfully"
  },
  "message": "Payment processed successfully"
}
```

#### Get Payment by ID
```http
GET /api/v1/Payments/{id}
```
**Description:** Get specific payment by ID
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): Payment ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "amount": 29.99,
    "currency": "USD",
    "description": "Contact Form Addon Purchase",
    "status": "Succeeded",
    "transactionId": "stripe_tx_12345",
    "createdAt": "2024-01-01T00:00:00Z",
    "processedAt": "2024-01-01T00:01:00Z",
    "tenantId": "guid",
    "userId": "guid",
    "subscriptionId": null,
    "invoiceId": null,
    "metadata": {}
  },
  "message": "Payment retrieved successfully"
}
```

#### Refund Payment
```http
POST /api/v1/Payments/{id}/refund
```
**Description:** Process a refund for a payment (Admin only)
**Authentication:** Bearer Token Required (Admin or PlatformAdmin)

**Path Parameters:**
- `id` (guid, required): Payment ID

**Request Body:**
```json
{
  "amount": 29.99,
  "reason": "Customer request"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "refundId": "guid",
    "amount": 29.99,
    "currency": "USD",
    "status": "Succeeded",
    "processedAt": "2024-01-01T12:00:00Z",
    "isSuccess": true
  },
  "message": "Refund processed successfully"
}
```

#### Payment Webhook
```http
POST /api/v1/Payments/webhook
```
**Description:** Handle payment webhooks from payment processors
**Authentication:** None (Webhook signature verification)

**Request Body:**
```json
{
  "eventType": "payment.succeeded",
  "data": {
    "paymentId": "guid",
    "amount": 29.99,
    "currency": "USD",
    "status": "succeeded"
  },
  "timestamp": "2024-01-01T00:01:00Z"
}
```

**Response:** `202 Accepted`
```json
{
  "success": true,
  "message": "Webhook processed successfully"
}
```

## Subscription Management

### Base Route: `/api/v1/Subscriptions`

#### Get Subscriptions
```http
GET /api/v1/Subscriptions
```
**Description:** Get user's subscriptions
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "guid",
      "userId": "guid",
      "planId": "guid",
      "subscriptionPlanId": "guid",
      "planName": "Pro Plan",
      "planPrice": 29.99,
      "currency": "USD",
      "billingCycle": "Monthly",
      "status": "Active",
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": null,
      "nextBillingDate": "2024-02-01T00:00:00Z",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "message": "Subscriptions retrieved successfully"
}
```

#### Get Subscription Plans
```http
GET /api/v1/Subscriptions/plans
```
**Description:** Get available subscription plans
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": [
    {
      "id": "guid",
      "name": "Pro Plan",
      "description": "Professional features for growing businesses",
      "price": 29.99,
      "currency": "USD",
      "billingCycle": "Monthly",
      "features": [
        "Unlimited sites",
        "Advanced components",
        "Priority support"
      ],
      "isActive": true,
      "sortOrder": 1
    }
  ],
  "message": "Subscription plans retrieved successfully"
}
```

#### Create Subscription
```http
POST /api/v1/Subscriptions
```
**Description:** Create a new subscription
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "planId": "guid",
  "paymentMethodId": "pm_1234567890",
  "startDate": "2024-01-01T00:00:00Z"
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "userId": "guid",
    "planId": "guid",
    "planName": "Pro Plan",
    "status": "Active",
    "startDate": "2024-01-01T00:00:00Z",
    "nextBillingDate": "2024-02-01T00:00:00Z"
  },
  "message": "Subscription created successfully"
}
```

## Administrative Functions

### Base Route: `/api/v1/Admin`

#### Get All Users (Admin)
```http
GET /api/v1/Admin/users
```
**Description:** Get all users across all tenants (Admin only)
**Authentication:** Bearer Token Required (Admin)

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Sort field
- `sortOrder` (string, default: "asc"): Sort order (asc/desc)
- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "tenantId": "guid",
        "userName": "<EMAIL>",
        "email": "<EMAIL>",
        "emailConfirmed": true,
        "role": "PlatformUser",
        "firstName": "John",
        "lastName": "Doe",
        "lastLoginAt": "2024-01-01T12:00:00Z",
        "isLockedOut": false,
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "All users retrieved successfully"
}
```

#### Get System Health
```http
GET /api/v1/Admin/system-health
```
**Description:** Get system health status
**Authentication:** Bearer Token Required (Admin)

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "status": "Healthy",
    "timestamp": "2024-01-01T12:00:00Z",
    "version": "1.0.0",
    "environment": "Production",
    "uptime": "5 days, 12 hours",
    "memoryUsage": "512 MB",
    "cpuUsage": "15%",
    "databaseStatus": "Connected",
    "cacheStatus": "Connected"
  },
  "message": "System health retrieved successfully"
}
```

#### Get All Configurations
```http
GET /api/v1/Admin/configurations
```
**Description:** Get all system configurations
**Authentication:** Bearer Token Required (Admin)

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Sort field
- `sortOrder` (string, default: "asc"): Sort order (asc/desc)
- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "key": "max-sites-per-tenant",
        "value": "10",
        "description": "Maximum sites per tenant",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "All configurations retrieved successfully"
}
```

#### Get Configuration by Key
```http
GET /api/v1/Admin/configurations/{key}
```
**Description:** Get specific configuration by key
**Authentication:** Bearer Token Required (Admin)

**Path Parameters:**
- `key` (string, required): Configuration key

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "key": "max-sites-per-tenant",
    "value": "10",
    "description": "Maximum sites per tenant",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "Configuration retrieved successfully"
}
```

#### Create Configuration
```http
POST /api/v1/Admin/configurations
```
**Description:** Create a new system configuration
**Authentication:** Bearer Token Required (Admin)

**Request Body:**
```json
{
  "key": "new-config-key",
  "value": "config-value",
  "description": "New configuration setting"
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "key": "new-config-key",
    "value": "config-value",
    "description": "New configuration setting",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "Configuration created successfully"
}
```

#### Update Configuration
```http
PUT /api/v1/Admin/configurations/{key}
```
**Description:** Update system configuration
**Authentication:** Bearer Token Required (Admin)

**Path Parameters:**
- `key` (string, required): Configuration key

**Request Body:**
```json
{
  "value": "updated-value",
  "description": "Updated configuration setting"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "key": "config-key",
    "value": "updated-value",
    "description": "Updated configuration setting",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T12:00:00Z"
  },
  "message": "Configuration updated successfully"
}
```

#### Delete Configuration
```http
DELETE /api/v1/Admin/configurations/{key}
```
**Description:** Delete system configuration
**Authentication:** Bearer Token Required (Admin)

**Path Parameters:**
- `key` (string, required): Configuration key

**Response:** `204 No Content`

## Security Features

### Base Route: `/api/v1/Security`

#### Get Security Scans
```http
GET /api/v1/Security/scans
```
**Description:** Get vulnerability scans for current tenant
**Authentication:** Bearer Token Required

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "targetUrl": "https://example.com",
        "status": "Completed",
        "startedAt": "2024-01-01T10:00:00Z",
        "completedAt": "2024-01-01T11:00:00Z",
        "vulnerabilitiesFound": 0,
        "severity": "Low"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "Vulnerability scans retrieved successfully"
}
```

#### Start Security Scan
```http
POST /api/v1/Security/scans
```
**Description:** Start a new vulnerability scan
**Authentication:** Bearer Token Required

**Request Body:**
```json
{
  "targetUrl": "https://mysite.com",
  "scanDepth": 3,
  "includeDependencies": true
}
```

**Response:** `201 Created`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "targetUrl": "https://mysite.com",
    "status": "Started",
    "startedAt": "2024-01-01T12:00:00Z",
    "scanDepth": 3,
    "includeDependencies": true
  },
  "message": "Vulnerability scan triggered successfully"
}
```

## Tenant Management

### Base Route: `/api/v1/Tenant` and `/api/v1/Tenants`

#### Get Current Tenant
```http
GET /api/v1/Tenant
```
**Description:** Get current tenant information
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "name": "My Company",
    "domain": "mycompany.com",
    "status": "Active",
    "createdAt": "2024-01-01T00:00:00Z",
    "slug": "mycompany",
    "subscriptionPlan": "Pro",
    "maxSites": 10,
    "maxUsers": 50,
    "isolationLevel": "Standard",
    "isolationEnforcedDate": null,
    "updatedAt": "2024-01-01T00:00:00Z",
    "isActive": true
  },
  "message": "Current tenant retrieved successfully"
}
```

#### Get All Tenants
```http
GET /api/v1/Tenants
```
**Description:** Get all tenants (Admin only)
**Authentication:** Bearer Token Required (Admin)

**Query Parameters:**
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Sort field
- `sortOrder` (string, default: "asc"): Sort order (asc/desc)
- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "name": "Company A",
        "domain": "companya.com",
        "status": "Active",
        "createdAt": "2024-01-01T00:00:00Z",
        "slug": "companya",
        "subscriptionPlan": "Pro",
        "maxSites": 10,
        "maxUsers": 50,
        "isolationLevel": "Standard",
        "isolationEnforcedDate": null,
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "All tenants retrieved successfully"
}
```

## File Management

### Base Route: `/api/v1/Files`

#### Get Files
```http
GET /api/v1/Files
```
**Description:** Get file overview and management
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "totalFiles": 25,
    "totalSize": "15.2 MB",
    "categories": [
      {
        "name": "Images",
        "count": 15,
        "size": "12.1 MB"
      },
      {
        "name": "Documents",
        "count": 8,
        "size": "2.8 MB"
      },
      {
        "name": "Videos",
        "count": 2,
        "size": "0.3 MB"
      }
    ],
    "recentFiles": [
      {
        "id": "guid",
        "name": "header-image.jpg",
        "size": "245 KB",
        "type": "image/jpeg",
        "uploadedAt": "2024-01-01T12:00:00Z"
      }
    ]
  },
  "message": "Files overview retrieved successfully"
}
```

## Error Handling

All API endpoints return consistent error responses in the following format:

### Error Response Structure
```json
{
  "success": false,
  "data": null,
  "message": "Error description",
  "errors": [
    {
      "field": "email",
      "message": "Email is required"
    }
  ]
}
```

### HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `204 No Content` - Request successful, no content returned
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict
- `422 Unprocessable Entity` - Validation errors
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## Rate Limiting

The API implements rate limiting on sensitive endpoints:

- **Authentication endpoints** (`/api/v1/Auth/login`, `/api/v1/Auth/api-key`): Limited to prevent brute force attacks
- **Registration endpoint** (`/api/v1/Auth/register`): Limited to prevent spam

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Authentication

### JWT Token Format
```
Authorization: Bearer <jwt_token>
```

### API Key Authentication
```
X-API-Key: <api_key>
```

### Token Expiration
- **Access Token**: 1 hour
- **Refresh Token**: 30 days
- **API Key**: 1 year (configurable)

## Pagination

All list endpoints support pagination with the following query parameters:

- `pageNumber` (int, default: 1): Page number (1-based)
- `pageSize` (int, default: 10): Items per page (max: 100)
- `sortBy` (string, optional): Field to sort by
- `sortOrder` (string, default: "asc"): Sort order ("asc" or "desc")

### Pagination Response Format
```json
{
  "data": [...],
  "totalCount": 100,
  "pageNumber": 1,
  "pageSize": 10,
  "totalPages": 10
}
```

## Filtering and Search

Many endpoints support filtering and search:

- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search across relevant fields

## Multi-Tenancy

The API is designed with multi-tenancy in mind:

- All data is automatically scoped to the current tenant
- Tenant isolation is enforced at the database level
- Cross-tenant access is restricted unless explicitly allowed for admin users

## GDPR Compliance

The API includes GDPR compliance features:

- **Data Export**: Users can export their personal data
- **Data Anonymization**: Admin can anonymize user data
- **Consent Management**: Track and manage user consents
- **Right to be Forgotten**: Complete data removal capabilities

---

**API Version:** v1
**Last Updated:** 2024-01-01
**Support:** <EMAIL>
{
  "success": true,
  "message": "Email confirmed successfully. You can now log in"
}
```

#### Resend Confirmation Email
```http
POST /api/v1/Auth/resend-confirmation-email
```
**Description:** Resend email confirmation link  
**Authentication:** None

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:** `200 OK`
```json
{
  "success": true,
  "message": "If an unconfirmed account with that email exists, a new confirmation email has been sent"
}
```

## User Management

### Base Route: `/api/v1/Users`

#### Get Users
```http
GET /api/v1/Users
```
**Description:** Get paginated list of users  
**Authentication:** Bearer Token Required

**Query Parameters:**
- `tenantId` (guid, optional): Filter by tenant ID
- `pageNumber` (int, default: 1): Page number
- `pageSize` (int, default: 10): Items per page
- `sortBy` (string, optional): Sort field
- `sortOrder` (string, default: "asc"): Sort order (asc/desc)
- `filter` (string, optional): Filter criteria
- `searchTerm` (string, optional): Search term

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "data": [
      {
        "id": "guid",
        "userId": "guid",
        "userName": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "tenantId": "guid",
        "role": "PlatformUser",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z"
      }
    ],
    "totalCount": 1,
    "pageNumber": 1,
    "pageSize": 10,
    "totalPages": 1
  },
  "message": "Users retrieved successfully"
}
```

#### Get User by ID
```http
GET /api/v1/Users/<USER>
```
**Description:** Get specific user by ID  
**Authentication:** Bearer Token Required

**Path Parameters:**
- `id` (guid, required): User ID

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "userId": "guid",
    "userName": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "tenantId": "guid",
    "role": "PlatformUser",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "User retrieved successfully"
}
```

#### Get Current User Profile
```http
GET /api/v1/Users/<USER>
```
**Description:** Get current user's profile  
**Authentication:** Bearer Token Required

**Response:** `200 OK`
```json
{
  "success": true,
  "data": {
    "id": "guid",
    "userId": "guid",
    "userName": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "tenantId": "guid",
    "role": "PlatformUser",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "User profile retrieved successfully"
}
```
